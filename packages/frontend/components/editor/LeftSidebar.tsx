'use client';

import { useState } from 'react';
import {
  FolderOpen,
  Image,
  MousePointer,
  Type,
  Hand,
  ZoomIn,
  Grid3X3,
  Ruler,
  Plus,
  Upload,
  FileText,
  Settings
} from 'lucide-react';
import { useEditor } from './EditorContext';

export default function LeftSidebar() {
  const { state, dispatch } = useEditor();
  const [activeTab, setActiveTab] = useState<'projects' | 'pages' | 'tools'>('tools');

  const tools = [
    { id: 'select', icon: MousePointer, label: 'Select' },
    { id: 'text-region', icon: Type, label: 'Text Region' },
    { id: 'pan', icon: Hand, label: 'Pan' },
    { id: 'zoom', icon: ZoomIn, label: 'Zoom' },
  ] as const;

  const handleToolSelect = (toolId: typeof tools[number]['id']) => {
    dispatch({ type: 'SET_SELECTED_TOOL', payload: toolId });
  };

  return (
    <div className="h-full bg-white border-r border-gray-200 flex flex-col">
      {/* Sidebar Header */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">Manga Editor</h2>
      </div>

      {/* Tab Navigation */}
      <div className="flex border-b border-gray-200">
        {[
          { id: 'tools', label: 'Tools', icon: MousePointer },
          { id: 'projects', label: 'Projects', icon: FolderOpen },
          { id: 'pages', label: 'Pages', icon: Image },
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex-1 px-3 py-2 text-sm font-medium border-b-2 transition-colors ${activeTab === tab.id
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
          >
            <tab.icon className="w-4 h-4 mx-auto mb-1" />
            <div className="text-xs">{tab.label}</div>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-y-auto">
        {activeTab === 'tools' && (
          <div className="p-4 space-y-4">
            {/* Tool Palette */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">Tools</h3>
              <div className="grid grid-cols-2 gap-2">
                {tools.map((tool) => (
                  <button
                    key={tool.id}
                    onClick={() => handleToolSelect(tool.id)}
                    className={`p-3 rounded-lg border-2 transition-all ${state.selectedTool === tool.id
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-200 hover:border-gray-300 text-gray-600'
                      }`}
                  >
                    <tool.icon className="w-5 h-5 mx-auto mb-1" />
                    <div className="text-xs font-medium">{tool.label}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* View Options */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">View</h3>
              <div className="space-y-2">
                <button
                  onClick={() => dispatch({ type: 'TOGGLE_GRID' })}
                  className={`w-full flex items-center px-3 py-2 rounded-lg text-sm transition-colors ${state.showGrid
                    ? 'bg-blue-50 text-blue-700'
                    : 'text-gray-600 hover:bg-gray-50'
                    }`}
                >
                  <Grid3X3 className="w-4 h-4 mr-2" />
                  Show Grid
                </button>
                <button
                  onClick={() => dispatch({ type: 'TOGGLE_RULERS' })}
                  className={`w-full flex items-center px-3 py-2 rounded-lg text-sm transition-colors ${state.showRulers
                    ? 'bg-blue-50 text-blue-700'
                    : 'text-gray-600 hover:bg-gray-50'
                    }`}
                >
                  <Ruler className="w-4 h-4 mr-2" />
                  Show Rulers
                </button>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'projects' && (
          <div className="p-4 space-y-4">
            {/* Project Actions */}
            <div className="space-y-2">
              <button className="w-full flex items-center px-3 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors">
                <Plus className="w-4 h-4 mr-2" />
                New Project
              </button>
              <button className="w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors">
                <FolderOpen className="w-4 h-4 mr-2" />
                Open Project
              </button>
            </div>

            {/* Recent Projects */}
            <div>
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">
                Recent Projects
              </h3>
              <div className="space-y-1">
                <div className="p-3 bg-gray-50 rounded-lg border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">Sample Manga</h4>
                      <p className="text-xs text-gray-500">3 pages • Last edited 2h ago</p>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                      <span className="text-xs text-gray-500">Active</span>
                    </div>
                  </div>
                </div>

                <div className="p-3 bg-white rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors cursor-pointer">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">Chapter 1</h4>
                      <p className="text-xs text-gray-500">12 pages • Last edited 1d ago</p>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                      <span className="text-xs text-gray-500">Draft</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'pages' && (
          <div className="p-4 space-y-4">
            {/* Page Actions */}
            <div className="space-y-2">
              <button className="w-full flex items-center px-3 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors">
                <Upload className="w-4 h-4 mr-2" />
                Upload Pages
              </button>
            </div>

            {/* Page Thumbnails */}
            <div>
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">
                Pages (3)
              </h3>
              <div className="space-y-2">
                {/* Sample page thumbnails */}
                {[1, 2, 3].map((pageNum) => (
                  <div
                    key={pageNum}
                    className="flex items-center p-2 bg-white rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors cursor-pointer"
                  >
                    <div className="w-12 h-16 bg-gray-200 rounded border mr-3 flex items-center justify-center">
                      <FileText className="w-6 h-6 text-gray-400" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 truncate">
                        Page {pageNum}
                      </h4>
                      <p className="text-xs text-gray-500">
                        {pageNum === 1 ? '2 regions' : pageNum === 2 ? '5 regions' : '3 regions'}
                      </p>
                      <div className="flex items-center mt-1">
                        <div className={`w-2 h-2 rounded-full mr-1 ${pageNum === 1 ? 'bg-green-400' : pageNum === 2 ? 'bg-yellow-400' : 'bg-gray-300'
                          }`}></div>
                        <span className="text-xs text-gray-500">
                          {pageNum === 1 ? 'Translated' : pageNum === 2 ? 'In Progress' : 'Not Started'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
