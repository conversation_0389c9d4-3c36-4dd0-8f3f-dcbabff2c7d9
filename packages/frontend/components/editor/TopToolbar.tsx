'use client';

import {
  Save,
  Download,
  Upload,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  RotateCw,
  Eye,
  EyeOff,
  Settings,
  Play
} from 'lucide-react';
import { useEditor } from './EditorContext';

export default function TopToolbar() {
  const { state, dispatch } = useEditor();

  const handleZoomIn = () => {
    // Clear any pending preview state to ensure immediate update
    window.dispatchEvent(new CustomEvent('clear-zoom-preview'));
    dispatch({ type: 'SET_ZOOM', payload: state.zoom * 1.2 });
  };

  const handleZoomOut = () => {
    // Clear any pending preview state to ensure immediate update
    window.dispatchEvent(new CustomEvent('clear-zoom-preview'));
    dispatch({ type: 'SET_ZOOM', payload: state.zoom / 1.2 });
  };

  const handleZoomReset = () => {
    // Clear any pending preview state to ensure immediate update
    window.dispatchEvent(new CustomEvent('clear-zoom-preview'));
    dispatch({ type: 'SET_ZOOM', payload: 1 });
    dispatch({ type: 'SET_VIEWPORT_OFFSET', payload: { x: 0, y: 0 } });
  };

  const handleZoomFit = () => {
    // Calculate zoom to fit the manga page (400x600) in the canvas
    const pageWidth = 400;
    const pageHeight = 600;
    const padding = 100;

    const scaleX = (state.canvasSize.width - padding) / pageWidth;
    const scaleY = (state.canvasSize.height - padding) / pageHeight;
    const scale = Math.min(scaleX, scaleY, 1); // Don't zoom in beyond 100%

    dispatch({ type: 'SET_ZOOM', payload: scale });

    // Center the page
    const offsetX = (state.canvasSize.width - pageWidth * scale) / 2;
    const offsetY = (state.canvasSize.height - pageHeight * scale) / 2;
    dispatch({ type: 'SET_VIEWPORT_OFFSET', payload: { x: offsetX, y: offsetY } });
  };

  return (
    <div className="h-14 bg-white border-b border-gray-200 flex items-center justify-between px-4">
      {/* Left Section - File Operations */}
      <div className="flex items-center space-x-2">
        <button className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
          <Upload className="w-4 h-4 mr-2" />
          Open
        </button>
        <button className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
          <Save className="w-4 h-4 mr-2" />
          Save
        </button>
        <button className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
          <Download className="w-4 h-4 mr-2" />
          Export
        </button>

        <div className="w-px h-6 bg-gray-300 mx-2" />

        <button className="flex items-center px-3 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors">
          <Play className="w-4 h-4 mr-2" />
          Process OCR
        </button>
      </div>

      {/* Center Section - Zoom Controls */}
      <div className="flex items-center space-x-2">
        <button
          onClick={handleZoomOut}
          className="p-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
          title="Zoom Out"
        >
          <ZoomOut className="w-4 h-4" />
        </button>

        <div className="flex items-center space-x-2 px-3 py-1 bg-gray-100 rounded-lg">
          <button
            onClick={handleZoomReset}
            className="text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
            title="Reset Zoom"
          >
            {Math.round(state.zoom * 100)}%
          </button>
        </div>

        <button
          onClick={handleZoomIn}
          className="p-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
          title="Zoom In"
        >
          <ZoomIn className="w-4 h-4" />
        </button>

        <div className="w-px h-6 bg-gray-300 mx-2" />

        <button
          onClick={handleZoomFit}
          className="px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
          title="Zoom to Fit"
        >
          Fit
        </button>

        {/* Zoom Presets */}
        <div className="flex items-center space-x-1">
          {[0.5, 1, 1.5, 2].map((zoomLevel) => (
            <button
              key={zoomLevel}
              onClick={() => dispatch({ type: 'SET_ZOOM', payload: zoomLevel })}
              className={`px-2 py-1 text-xs rounded transition-colors ${Math.abs(state.zoom - zoomLevel) < 0.1
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:bg-gray-100'
                }`}
              title={`${zoomLevel * 100}%`}
            >
              {zoomLevel * 100}%
            </button>
          ))}
        </div>
      </div>

      {/* Right Section - View Options */}
      <div className="flex items-center space-x-2">
        <button
          onClick={() => dispatch({ type: 'TOGGLE_GRID' })}
          className={`p-2 rounded-lg transition-colors ${state.showGrid
            ? 'bg-blue-100 text-blue-700'
            : 'text-gray-700 hover:bg-gray-100'
            }`}
          title="Toggle Grid"
        >
          {state.showGrid ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
        </button>

        <button
          onClick={() => dispatch({ type: 'TOGGLE_RULERS' })}
          className={`p-2 rounded-lg transition-colors ${state.showRulers
            ? 'bg-blue-100 text-blue-700'
            : 'text-gray-700 hover:bg-gray-100'
            }`}
          title="Toggle Rulers"
        >
          <RotateCcw className="w-4 h-4" />
        </button>

        <div className="w-px h-6 bg-gray-300 mx-2" />

        <button className="p-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
          <Settings className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
}
