'use client';

import { Rect } from 'react-konva';
import { useEditor } from './EditorContext';

interface DrawingState {
  isDrawing: boolean;
  startX: number;
  startY: number;
  currentX: number;
  currentY: number;
}

interface DrawingPreview {
  currentX: number;
  currentY: number;
}

interface TextRegionDrawToolProps {
  drawingState: DrawingState;
  drawingPreview: DrawingPreview | null;
  effectiveZoom?: number; // Optional zoom override for smooth zoom compatibility
}

export default function TextRegionDrawTool({ drawingState, drawingPreview, effectiveZoom }: TextRegionDrawToolProps) {
  const { state } = useEditor();

  // Calculate drawing rectangle dimensions using preview coordinates when available
  const currentX = drawingPreview?.currentX ?? drawingState.currentX;
  const currentY = drawingPreview?.currentY ?? drawingState.currentY;

  const drawingRect = {
    x: Math.min(drawingState.startX, currentX),
    y: Math.min(drawingState.startY, currentY),
    width: Math.abs(currentX - drawingState.startX),
    height: Math.abs(currentY - drawingState.startY),
  };

  // Calculate stroke width inversely proportional to zoom to maintain constant visual thickness
  // Use effectiveZoom if provided (for smooth zoom compatibility), otherwise use state.zoom
  const currentZoom = effectiveZoom ?? state.zoom;
  const baseStrokeWidth = 2;
  const strokeWidth = baseStrokeWidth / currentZoom;

  return (
    <>
      {/* Drawing preview rectangle - overlay removed to fix panning conflict */}
      {drawingState.isDrawing && drawingRect.width > 0 && drawingRect.height > 0 && (
        <Rect
          x={drawingRect.x}
          y={drawingRect.y}
          width={drawingRect.width}
          height={drawingRect.height}
          fill="rgba(59, 130, 246, 0.2)"
          stroke="#3b82f6"
          strokeWidth={strokeWidth}
          dash={[5, 5]}
          listening={false}
        />
      )}
    </>
  );
}
