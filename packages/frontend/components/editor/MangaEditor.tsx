'use client';

import { useState } from 'react';
import dynamic from 'next/dynamic';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import LeftSidebar from './LeftSidebar';
import RightSidebar from './RightSidebar';
import TopToolbar from './TopToolbar';
import StatusBar from './StatusBar';
import { EditorProvider } from './EditorContext';

// Dynamically import CanvasWorkspace to avoid SSR issues with Konva.js
const CanvasWorkspace = dynamic(() => import('./CanvasWorkspace'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full bg-gray-100 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
        <p className="text-gray-600">Loading Canvas...</p>
      </div>
    </div>
  ),
});

export default function MangaEditor() {
  return (
    <EditorProvider>
      <div className="h-screen flex flex-col bg-gray-50">
        {/* Top Toolbar */}
        <TopToolbar />

        {/* Main Content Area */}
        <div className="flex-1 flex">
          <PanelGroup direction="horizontal" className="h-full">
            {/* Left Sidebar */}
            <Panel defaultSize={20} minSize={15} maxSize={30}>
              <LeftSidebar />
            </Panel>

            <PanelResizeHandle className="w-1 bg-gray-300 hover:bg-gray-400 transition-colors" />

            {/* Main Canvas Area */}
            <Panel defaultSize={60} minSize={40}>
              <CanvasWorkspace />
            </Panel>

            <PanelResizeHandle className="w-1 bg-gray-300 hover:bg-gray-400 transition-colors" />

            {/* Right Sidebar */}
            <Panel defaultSize={20} minSize={15} maxSize={30}>
              <RightSidebar />
            </Panel>
          </PanelGroup>
        </div>

        {/* Status Bar */}
        <StatusBar />
      </div>
    </EditorProvider>
  );
}
