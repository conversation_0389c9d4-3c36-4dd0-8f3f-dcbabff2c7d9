/**
 * TypeScript type definitions for ho-trans backend API responses
 * Generated from backend Pydantic schemas
 */

// Enums matching backend constants
export enum ProjectStatus {
  DRAFT = "draft",
  IN_PROGRESS = "in_progress",
  COMPLETED = "completed",
  ARCHIVED = "archived"
}

export enum OCRStatus {
  PENDING = "pending",
  PROCESSING = "processing",
  COMPLETED = "completed",
  FAILED = "failed"
}

export enum TranslationStatus {
  PENDING = "pending",
  IN_PROGRESS = "in_progress",
  COMPLETED = "completed",
  FAILED = "failed"
}

export enum TextRegionType {
  SPEECH_BUBBLE = "speech_bubble",
  THOUGHT_BUBBLE = "thought_bubble",
  NARRATION = "narration",
  SOUND_EFFECT = "sound_effect",
  SIGN = "sign",
  OTHER = "other"
}

export enum LLMProvider {
  CLAUDE = "claude",
  OPENAI = "openai",
  GEMINI = "gemini",
  DEEPSEEK = "deepseek"
}

// Base schema interface
export interface BaseSchema {
  id: string;
  created_at: string;
  updated_at: string;
}

// Project interfaces
export interface ProjectCreate {
  name: string;
  description?: string;
  source_language?: string;
  target_language?: string;
}

export interface ProjectUpdate {
  name?: string;
  description?: string;
  status?: ProjectStatus;
  source_language?: string;
  target_language?: string;
}

export interface ProjectResponse extends BaseSchema {
  name: string;
  description?: string;
  status: ProjectStatus;
  source_language: string;
  target_language: string;
  page_count: number;
}

// Project Page interfaces
export interface ProjectPageCreate {
  page_number: number;
  original_filename: string;
}

export interface ProjectPageResponse extends BaseSchema {
  project_id: string;
  page_number: number;
  original_filename: string;
  file_path: string;
  file_size: number;
  image_width?: number;
  image_height?: number;
  ocr_status: OCRStatus;
  text_region_count: number;
}

// Text Region interfaces
export interface TextRegionCreate {
  region_type?: TextRegionType;
  x: number; // normalized 0-1
  y: number; // normalized 0-1
  width: number; // normalized 0-1
  height: number; // normalized 0-1
  original_text?: string;
  confidence_score?: number;
  background_opacity?: number; // 0.0 to 1.0
}

export interface TextRegionUpdate {
  region_type?: TextRegionType;
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  original_text?: string;
  translated_text?: string;
  translation_status?: TranslationStatus;
  font_family?: string;
  font_size?: number;
  font_color?: string; // hex color
  background_color?: string; // hex color or 'transparent'
  background_opacity?: number; // 0.0 to 1.0
}

export interface TextRegionResponse extends BaseSchema {
  page_id: string;
  region_type: TextRegionType;
  x: number;
  y: number;
  width: number;
  height: number;
  original_text?: string;
  confidence_score?: number;
  translated_text?: string;
  translation_status: TranslationStatus;
  font_family?: string;
  font_size?: number;
  font_color?: string;
  background_color?: string;
  background_opacity?: number; // 0.0 to 1.0
}

// Detailed responses
export interface ProjectDetailResponse extends ProjectResponse {
  pages: ProjectPageResponse[];
}

export interface ProjectPageDetailResponse extends ProjectPageResponse {
  text_regions: TextRegionResponse[];
}

// OCR interfaces
export interface OCRJobCreate {
  page_id: string;
  provider?: LLMProvider;
  custom_prompt?: string;
}

export interface OCRJobResponse extends BaseSchema {
  page_id: string;
  provider: LLMProvider;
  status: OCRStatus;
  custom_prompt?: string;
  error_message?: string;
  processing_time?: number;
  result_count: number;
}

export interface OCRResultResponse extends BaseSchema {
  job_id: string;
  detected_text: string;
  confidence_score?: number;
  region_type?: TextRegionType;
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  metadata?: Record<string, any>;
}

export interface OCRJobDetailResponse extends OCRJobResponse {
  results: OCRResultResponse[];
}

export interface OCRProcessRequest {
  page_id: string;
  provider?: LLMProvider;
  custom_prompt?: string;
}

export interface OCRBatchProcessRequest {
  page_ids: string[];
  provider?: LLMProvider;
  custom_prompt?: string;
}

export interface OCRStatistics {
  total_jobs: number;
  completed_jobs: number;
  failed_jobs: number;
  pending_jobs: number;
  processing_jobs: number;
  average_processing_time?: number;
  average_confidence_score?: number;
  provider_usage?: Record<string, number>;
}

// Translation interfaces
export interface TranslationJobCreate {
  text_region_id: string;
  provider?: LLMProvider;
  source_language: string;
  target_language: string;
  original_text: string;
  custom_prompt?: string;
}

export interface TranslationJobResponse extends BaseSchema {
  text_region_id: string;
  provider: LLMProvider;
  status: TranslationStatus;
  source_language: string;
  target_language: string;
  original_text: string;
  custom_prompt?: string;
  error_message?: string;
  processing_time?: number;
  alternative_count: number;
}

export interface TranslationAlternativeResponse extends BaseSchema {
  job_id: string;
  translated_text: string;
  confidence_score?: number;
  quality_score?: number;
  rank: number;
  is_selected: boolean;
  metadata?: Record<string, any>;
}

export interface TranslationJobDetailResponse extends TranslationJobResponse {
  alternatives: TranslationAlternativeResponse[];
}

export interface TranslationProcessRequest {
  text_region_id: string;
  provider?: LLMProvider;
  source_language: string;
  target_language: string;
  original_text: string;
  custom_prompt?: string;
}

export interface TranslationBatchProcessRequest {
  text_region_ids: string[];
  provider?: LLMProvider;
  source_language: string;
  target_language: string;
  custom_prompt?: string;
}

export interface TranslationStatistics {
  total_jobs: number;
  completed_jobs: number;
  failed_jobs: number;
  pending_jobs: number;
  processing_jobs: number;
  average_processing_time?: number;
  average_confidence_score?: number;
  average_quality_score?: number;
  language_pairs: Record<string, number>;
}

// LLM Provider interfaces
export interface LLMProviderInfo {
  provider: LLMProvider;
  is_available: boolean;
  default_model?: string;
  available_models: string[];
  supports_vision: boolean;
  api_key_configured: boolean;
}

export interface LLMProvidersStatus {
  providers: LLMProviderInfo[];
  default_provider?: LLMProvider;
  total_available: number;
}

// Pagination interfaces
export interface PaginationParams {
  page?: number;
  limit?: number;
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  pages: number;
  has_prev: boolean;
  has_next: boolean;
  prev_page?: number;
  next_page?: number;
}

export interface PaginatedResponse<T> {
  items: T[];
  meta: PaginationMeta;
}

// Error and Success responses
export interface ErrorResponse {
  error_code: string;
  detail: string;
  timestamp: string;
}

export interface SuccessResponse {
  message: string;
  data?: any;
}

// Supported languages
export const SUPPORTED_LANGUAGES = {
  japanese: "Japanese",
  english: "English",
  indonesian: "Indonesian",
  korean: "Korean",
  chinese: "Chinese"
} as const;

export type SupportedLanguage = keyof typeof SUPPORTED_LANGUAGES;
