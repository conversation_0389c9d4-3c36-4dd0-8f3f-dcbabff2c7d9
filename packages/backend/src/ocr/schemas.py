"""
Pydantic schemas for OCR processing.
"""
from typing import List, Optional, Dict, Any

from pydantic import Field, field_validator

from src.constants import OCRStatus, LLMProvider, TextRegionType
from src.schemas import BaseSchema, CustomModel


# OCR Job schemas
class OCRJobCreate(CustomModel):
    """Schema for creating a new OCR job."""

    page_id: str = Field(..., description="Project page ID")
    provider: LLMProvider = Field(
        default=LLMProvider.CLAUDE, description="LLM provider to use")
    custom_prompt: Optional[str] = Field(
        None, description="Custom prompt for OCR processing")


class OCRJobResponse(BaseSchema):
    """Schema for OCR job response."""

    page_id: str = Field(..., description="Project page ID")
    status: OCRStatus = Field(..., description="OCR processing status")
    provider: LLMProvider = Field(..., description="LLM provider used")
    prompt_used: Optional[str] = Field(
        None, description="Prompt used for processing")
    processing_time_seconds: Optional[float] = Field(
        None, description="Processing time in seconds")
    error_message: Optional[str] = Field(
        None, description="Error message if failed")
    retry_count: int = Field(default=0, description="Number of retry attempts")
    total_regions_detected: int = Field(
        default=0, description="Total text regions detected")
    average_confidence: Optional[float] = Field(
        None, description="Average confidence score")


# OCR Result schemas
class OCRResultCreate(CustomModel):
    """Schema for creating OCR results."""

    detected_text: str = Field(..., description="Detected text content")
    confidence_score: Optional[float] = Field(
        None, ge=0, le=1, description="Confidence score")
    region_type: Optional[TextRegionType] = Field(
        None, description="Type of text region")
    x: Optional[float] = Field(
        None, ge=0, le=1, description="X coordinate (normalized)")
    y: Optional[float] = Field(
        None, ge=0, le=1, description="Y coordinate (normalized)")
    width: Optional[float] = Field(
        None, gt=0, le=1, description="Width (normalized)")
    height: Optional[float] = Field(
        None, gt=0, le=1, description="Height (normalized)")
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Additional metadata")


class OCRResultResponse(BaseSchema):
    """Schema for OCR result response."""

    job_id: str = Field(..., description="OCR job ID")
    detected_text: str = Field(..., description="Detected text content")
    confidence_score: Optional[float] = Field(
        None, description="Confidence score")
    region_type: Optional[TextRegionType] = Field(
        None, description="Type of text region")
    x: Optional[float] = Field(None, description="X coordinate (normalized)")
    y: Optional[float] = Field(None, description="Y coordinate (normalized)")
    width: Optional[float] = Field(None, description="Width (normalized)")
    height: Optional[float] = Field(None, description="Height (normalized)")
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Additional metadata")


# Detailed OCR job response with results
class OCRJobDetailResponse(OCRJobResponse):
    """Schema for detailed OCR job response with results."""

    results: List[OCRResultResponse] = Field(
        default=[], description="OCR results")


# OCR processing request
class OCRProcessRequest(CustomModel):
    """Schema for OCR processing request."""

    page_id: str = Field(..., description="Project page ID")
    provider: Optional[LLMProvider] = Field(
        None, description="LLM provider to use")
    custom_prompt: Optional[str] = Field(
        None, description="Custom prompt for OCR processing")
    auto_create_regions: bool = Field(
        default=True, description="Automatically create text regions")


# OCR batch processing request
class OCRBatchProcessRequest(CustomModel):
    """Schema for batch OCR processing request."""

    project_id: str = Field(..., description="Project ID")
    page_ids: Optional[List[str]] = Field(
        None, description="Specific page IDs to process")
    provider: Optional[LLMProvider] = Field(
        None, description="LLM provider to use")
    custom_prompt: Optional[str] = Field(
        None, description="Custom prompt for OCR processing")
    auto_create_regions: bool = Field(
        default=True, description="Automatically create text regions")


# OCR statistics
class OCRStatistics(CustomModel):
    """Schema for OCR processing statistics."""

    total_jobs: int = Field(..., description="Total number of OCR jobs")
    completed_jobs: int = Field(..., description="Number of completed jobs")
    failed_jobs: int = Field(..., description="Number of failed jobs")
    pending_jobs: int = Field(..., description="Number of pending jobs")
    processing_jobs: int = Field(..., description="Number of jobs in progress")
    average_processing_time: Optional[float] = Field(
        None, description="Average processing time in seconds")
    total_regions_detected: int = Field(...,
                                        description="Total text regions detected")
    average_confidence: Optional[float] = Field(
        None, description="Average confidence score")


# LLM response format for structured OCR results
class LLMOCRRegion(CustomModel):
    """Schema for LLM OCR region response."""

    text: str = Field(..., description="Detected text")
    type: str = Field(..., description="Type of text region")
    confidence: Optional[float] = Field(
        None, ge=0, le=1, description="Confidence score")
    coordinates: Optional[Dict[str, float]] = Field(
        None, description="Bounding box coordinates")

    @field_validator("type")
    @classmethod
    def validate_region_type(cls, v):
        """Validate and normalize region type."""
        type_mapping = {
            "speech": TextRegionType.SPEECH_BUBBLE,
            "speech_bubble": TextRegionType.SPEECH_BUBBLE,
            "dialogue": TextRegionType.SPEECH_BUBBLE,
            "thought": TextRegionType.THOUGHT_BUBBLE,
            "thought_bubble": TextRegionType.THOUGHT_BUBBLE,
            "narration": TextRegionType.NARRATION,
            "narrative": TextRegionType.NARRATION,
            "sound": TextRegionType.SOUND_EFFECT,
            "sound_effect": TextRegionType.SOUND_EFFECT,
            "sfx": TextRegionType.SOUND_EFFECT,
            "sign": TextRegionType.SIGN,
            "text": TextRegionType.SIGN,
            "other": TextRegionType.OTHER,
        }

        normalized = v.lower().replace(" ", "_")
        return type_mapping.get(normalized, TextRegionType.OTHER)


class LLMOCRResponse(CustomModel):
    """Schema for LLM OCR response."""

    regions: List[LLMOCRRegion] = Field(...,
                                        description="Detected text regions")
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Additional metadata")
