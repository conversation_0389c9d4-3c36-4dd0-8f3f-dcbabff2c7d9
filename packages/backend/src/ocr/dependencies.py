"""
Dependencies for OCR processing endpoints.
"""
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from src.database import get_db
from src.ocr.service import OCRService
from src.ocr.schemas import OCRJobResponse


async def get_ocr_service(session: AsyncSession = Depends(get_db)) -> OCRService:
    """Dependency to get OCR service instance."""
    return OCRService(session)


async def valid_ocr_job_id(
    job_id: str,
    service: OCRService = Depends(get_ocr_service)
) -> OCRJobResponse:
    """Dependency to validate OCR job ID and return job data."""
    return await service.get_ocr_job(job_id)
