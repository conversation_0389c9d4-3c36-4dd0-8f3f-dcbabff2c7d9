"""
FastAPI router for OCR processing endpoints.
"""
from typing import List, Optional

from fastapi import APIRouter, Depends, status, BackgroundTasks

from src.ocr.dependencies import get_ocr_service, valid_ocr_job_id
from src.ocr.service import OCRService
from src.ocr.schemas import (
    OCRJobCreate,
    OCRJobResponse,
    OCRJobDetailResponse,
    OCRResultResponse,
    OCRProcessRequest,
    OCRBatchProcessRequest,
    OCRStatistics
)
from src.pagination import PaginationParams, PaginatedResponse, get_pagination_params
from src.schemas import SuccessResponse

router = APIRouter()


# OCR Job endpoints
@router.post(
    "/jobs",
    response_model=OCRJobResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create OCR job",
    description="Create a new OCR processing job"
)
async def create_ocr_job(
    job_data: OCRJobCreate,
    service: OCRService = Depends(get_ocr_service)
):
    """Create a new OCR job."""
    return await service.create_ocr_job(job_data)


@router.get(
    "/jobs/{job_id}",
    response_model=OCRJobResponse,
    summary="Get OCR job",
    description="Get OCR job information by ID"
)
async def get_ocr_job(
    job: OCRJobResponse = Depends(valid_ocr_job_id)
):
    """Get OCR job by ID."""
    return job


@router.get(
    "/jobs/{job_id}/detail",
    response_model=OCRJobDetailResponse,
    summary="Get detailed OCR job information",
    description="Get detailed OCR job information including results"
)
async def get_ocr_job_detail(
    job_id: str,
    service: OCRService = Depends(get_ocr_service)
):
    """Get detailed OCR job information."""
    return await service.get_ocr_job_detail(job_id)


@router.get(
    "/jobs/{job_id}/results",
    response_model=List[OCRResultResponse],
    summary="Get OCR results",
    description="Get all OCR results for a job"
)
async def get_ocr_results(
    job_id: str,
    _job: OCRJobResponse = Depends(valid_ocr_job_id),
    service: OCRService = Depends(get_ocr_service)
):
    """Get OCR results for a job."""
    return await service.get_ocr_results(job_id)


@router.get(
    "/pages/{page_id}/jobs",
    response_model=List[OCRJobResponse],
    summary="Get page OCR jobs",
    description="Get all OCR jobs for a page"
)
async def get_page_ocr_jobs(
    page_id: str,
    service: OCRService = Depends(get_ocr_service)
):
    """Get all OCR jobs for a page."""
    return await service.get_ocr_jobs_by_page(page_id)


# OCR Processing endpoints
@router.post(
    "/process",
    response_model=OCRJobResponse,
    status_code=status.HTTP_202_ACCEPTED,
    summary="Process OCR request",
    description="Start OCR processing for a page"
)
async def process_ocr(
    request: OCRProcessRequest,
    background_tasks: BackgroundTasks,
    service: OCRService = Depends(get_ocr_service)
):
    """Process OCR request."""
    job = await service.process_ocr_request(request)

    # Add background task for actual processing
    background_tasks.add_task(process_ocr_background, job.id, service)

    return job


@router.post(
    "/process/batch",
    response_model=List[OCRJobResponse],
    status_code=status.HTTP_202_ACCEPTED,
    summary="Process batch OCR request",
    description="Start OCR processing for multiple pages"
)
async def process_batch_ocr(
    request: OCRBatchProcessRequest,
    background_tasks: BackgroundTasks,
    service: OCRService = Depends(get_ocr_service)
):
    """Process batch OCR request."""
    from src.projects.service import ProjectService

    project_service = ProjectService(service.session)
    jobs = []

    # Get pages to process
    if request.page_ids:
        # Process specific pages
        page_ids = request.page_ids
    else:
        # Process all pages in the project
        project_pages = await project_service.get_project_pages(request.project_id)
        page_ids = [page.id for page in project_pages]

    # Create OCR jobs for each page
    for page_id in page_ids:
        try:
            # Create individual OCR request
            ocr_request = OCRProcessRequest(
                page_id=page_id,
                provider=request.provider,
                custom_prompt=request.custom_prompt
            )

            # Create the job
            job = await service.process_ocr_request(ocr_request)
            jobs.append(job)

            # Add background task for processing
            background_tasks.add_task(process_ocr_background, job.id, service)

        except Exception as e:
            # Log error but continue with other pages
            print(f"Failed to create OCR job for page {page_id}: {e}")
            continue

    return jobs


# Statistics endpoints
@router.get(
    "/statistics",
    response_model=OCRStatistics,
    summary="Get OCR statistics",
    description="Get OCR processing statistics"
)
async def get_ocr_statistics(
    project_id: Optional[str] = None,
    service: OCRService = Depends(get_ocr_service)
):
    """Get OCR statistics."""
    return await service.get_ocr_statistics(project_id)


# Utility endpoints
@router.post(
    "/jobs/{job_id}/retry",
    response_model=OCRJobResponse,
    summary="Retry OCR job",
    description="Retry a failed OCR job"
)
async def retry_ocr_job(
    job_id: str,
    background_tasks: BackgroundTasks,
    _job: OCRJobResponse = Depends(valid_ocr_job_id),
    service: OCRService = Depends(get_ocr_service)
):
    """Retry a failed OCR job."""
    # TODO: Implement retry logic
    # For now, just return the job
    return await service.get_ocr_job(job_id)


# Background task function
async def process_ocr_background(job_id: str, service: OCRService):
    """Background task for OCR processing."""
    import time
    from pathlib import Path
    from src.llm_providers.service import LLMService
    from src.projects.service import ProjectService
    from src.constants import OCRStatus

    start_time = time.time()
    llm_service = LLMService()

    try:
        # Get the job details
        job = await service.get_ocr_job(job_id)

        # Get the page details to load the image
        project_service = ProjectService(service.session)
        page = await project_service.get_project_page(job.page_id)

        # Load the image file
        image_path = Path(page.file_path)
        if not image_path.exists():
            await service.update_ocr_job_status(
                job_id,
                OCRStatus.FAILED,
                error_message=f"Image file not found: {page.file_path}"
            )
            return

        # Read image data
        with open(image_path, 'rb') as f:
            image_data = f.read()

        # Determine image format from file extension
        image_format = image_path.suffix.lower().replace('.', '')
        if image_format == 'jpg':
            image_format = 'jpeg'

        # Update job status to processing
        await service.update_ocr_job_status(job_id, OCRStatus.PROCESSING)

        # Perform OCR using LLM service
        ocr_response = await llm_service.perform_ocr(
            image_data=image_data,
            image_format=image_format,
            provider=job.provider,
            custom_prompt=job.prompt_used
        )

        # Convert LLM response to OCR results
        from src.ocr.schemas import OCRResultCreate
        ocr_results = []

        for region in ocr_response.regions:
            ocr_result = OCRResultCreate(
                detected_text=region.text,
                confidence_score=region.confidence,
                region_type=region.region_type,
                x=region.x,
                y=region.y,
                width=region.width,
                height=region.height,
                metadata=region.metadata or {}
            )
            ocr_results.append(ocr_result)

        # Save OCR results
        await service.save_ocr_results(job_id, ocr_results)

        # Calculate processing time
        processing_time = time.time() - start_time

        # Update job status to completed
        await service.update_ocr_job_status(
            job_id,
            OCRStatus.COMPLETED,
            processing_time=processing_time
        )

    except Exception as e:
        # Update job status to failed
        await service.update_ocr_job_status(
            job_id,
            OCRStatus.FAILED,
            error_message=str(e)
        )
        print(f"OCR processing failed for job {job_id}: {e}")
