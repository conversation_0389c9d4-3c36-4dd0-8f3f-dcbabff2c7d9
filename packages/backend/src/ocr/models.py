"""
SQLAlchemy models for OCR processing.
"""
from typing import List, Optional

from sqlalchemy import String, Text, Float, Integer, ForeignKey, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.constants import OCRStatus, LLMProvider
from src.models import BaseModel


class OCRJob(BaseModel):
    """OCR job model for tracking OCR processing tasks."""

    __tablename__ = "ocr_job"

    page_id: Mapped[str] = mapped_column(
        String(36), ForeignKey("project_page.id"))
    status: Mapped[str] = mapped_column(String(50), default=OCRStatus.PENDING)
    provider: Mapped[str] = mapped_column(
        String(50), default=LLMProvider.CLAUDE)

    # Processing details
    prompt_used: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    raw_response: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    processing_time_seconds: Mapped[Optional[float]
                                    ] = mapped_column(Float, nullable=True)

    # Error handling
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    retry_count: Mapped[int] = mapped_column(Integer, default=0)

    # Results metadata
    total_regions_detected: Mapped[int] = mapped_column(Integer, default=0)
    average_confidence: Mapped[Optional[float]
                               ] = mapped_column(Float, nullable=True)

    # Relationships
    page: Mapped["ProjectPage"] = relationship(foreign_keys="OCRJob.page_id")
    results: Mapped[List["OCRResult"]] = relationship(
        back_populates="job", cascade="all, delete-orphan")


class OCRResult(BaseModel):
    """OCR result model for individual text detection results."""

    __tablename__ = "ocr_result"

    job_id: Mapped[str] = mapped_column(String(36), ForeignKey("ocr_job.id"))

    # Text content
    detected_text: Mapped[str] = mapped_column(Text)
    confidence_score: Mapped[Optional[float]
                             ] = mapped_column(Float, nullable=True)

    # Region information
    region_type: Mapped[Optional[str]] = mapped_column(
        String(50), nullable=True)

    # Bounding box coordinates (normalized 0-1)
    x: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    y: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    width: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    height: Mapped[Optional[float]] = mapped_column(Float, nullable=True)

    # Additional metadata from LLM
    extra_data: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)

    # Relationships
    job: Mapped["OCRJob"] = relationship(back_populates="results")
