"""
OCR-specific exceptions.
"""
from src.constants import ErrorCode
from src.exceptions import NotFoundError, BadRequestError, InternalServerError


class OCRJobNotFound(NotFoundError):
    """OCR job not found exception."""
    
    def __init__(self, detail: str = "OCR job not found"):
        super().__init__(detail=detail, error_code="OCR_JOB_NOT_FOUND")


class OCRProcessingFailed(InternalServerError):
    """OCR processing failed exception."""
    
    def __init__(self, detail: str = "OCR processing failed"):
        super().__init__(detail=detail, error_code=ErrorCode.OCR_PROCESSING_FAILED)


class UnsupportedImageFormat(BadRequestError):
    """Unsupported image format exception."""
    
    def __init__(self, detail: str = "Unsupported image format"):
        super().__init__(detail=detail, error_code=ErrorCode.UNSUPPORTED_IMAGE_FORMAT)


class ImageTooLarge(BadRequestError):
    """Image too large exception."""
    
    def __init__(self, detail: str = "Image file is too large"):
        super().__init__(detail=detail, error_code=ErrorCode.IMAGE_TOO_LARGE)


class LLMAPIError(InternalServerError):
    """LLM API error exception."""
    
    def __init__(self, detail: str = "LLM API error"):
        super().__init__(detail=detail, error_code=ErrorCode.LLM_API_ERROR)


class LLMRateLimit(BadRequestError):
    """LLM rate limit exception."""
    
    def __init__(self, detail: str = "LLM API rate limit exceeded"):
        super().__init__(detail=detail, error_code=ErrorCode.LLM_RATE_LIMIT)


class InvalidAPIKey(BadRequestError):
    """Invalid API key exception."""
    
    def __init__(self, detail: str = "Invalid LLM API key"):
        super().__init__(detail=detail, error_code=ErrorCode.INVALID_API_KEY)
