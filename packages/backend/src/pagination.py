"""
Global pagination utilities for ho-trans application.
"""
from typing import Any, Dict, List, Optional, TypeVar, Generic
from math import ceil

from fastapi import Query
from pydantic import Field, field_validator
from sqlalchemy import func, select
from sqlalchemy.sql import Select
from sqlalchemy.ext.asyncio import AsyncSession

from src.schemas import CustomModel

T = TypeVar('T')


class PaginationParams(CustomModel):
    """Standard pagination parameters for list endpoints."""

    page: int = Field(default=1, ge=1, description="Page number (1-based)")
    limit: int = Field(default=10, ge=1, le=100,
                       description="Number of items per page")

    @property
    def offset(self) -> int:
        """Calculate offset from page and limit."""
        return (self.page - 1) * self.limit

    @field_validator("limit")
    def validate_limit(cls, v):
        """Ensure limit is within reasonable bounds."""
        if v > 100:
            raise ValueError("Limit cannot exceed 100 items per page")
        return v


class PaginationMeta(CustomModel):
    """Pagination metadata."""

    page: int = Field(..., description="Current page number")
    limit: int = Field(..., description="Items per page")
    total: int = Field(..., description="Total number of items")
    pages: int = Field(..., description="Total number of pages")
    has_prev: bool = Field(..., description="Whether there is a previous page")
    has_next: bool = Field(..., description="Whether there is a next page")
    prev_page: Optional[int] = Field(None, description="Previous page number")
    next_page: Optional[int] = Field(None, description="Next page number")


class PaginatedResponse(CustomModel, Generic[T]):
    """Generic paginated response wrapper."""

    items: List[T] = Field(..., description="List of items for current page")
    meta: PaginationMeta = Field(..., description="Pagination metadata")

    @classmethod
    def create(
        cls,
        items: List[T],
        total: int,
        params: PaginationParams
    ) -> "PaginatedResponse[T]":
        """Create a paginated response from items and parameters."""
        pages = ceil(total / params.limit) if total > 0 else 1
        has_prev = params.page > 1
        has_next = params.page < pages

        meta = PaginationMeta(
            page=params.page,
            limit=params.limit,
            total=total,
            pages=pages,
            has_prev=has_prev,
            has_next=has_next,
            prev_page=params.page - 1 if has_prev else None,
            next_page=params.page + 1 if has_next else None
        )

        return cls(items=items, meta=meta)


class Paginator:
    """Database paginator utility."""

    def __init__(self, session: AsyncSession):
        self.session = session

    async def paginate(
        self,
        query: Select,
        params: PaginationParams,
        count_query: Optional[Select] = None
    ) -> tuple[List[Dict[str, Any]], int]:
        """
        Paginate a SQLAlchemy query.

        Args:
            query: The main query to paginate
            params: Pagination parameters
            count_query: Optional custom count query (defaults to counting main query)

        Returns:
            Tuple of (items, total_count)
        """
        # Get total count
        if count_query is None:
            # Create count query from main query
            count_query = select(func.count()).select_from(query.subquery())

        total_result = await self.session.execute(count_query)
        total = total_result.scalar() or 0

        # Apply pagination to main query
        paginated_query = query.limit(params.limit).offset(params.offset)
        items_result = await self.session.execute(paginated_query)
        items = items_result.fetchall()

        return [dict(item._mapping) for item in items], total

    async def paginate_orm(
        self,
        query: Select,
        params: PaginationParams,
        count_query: Optional[Select] = None
    ) -> tuple[List[Any], int]:
        """
        Paginate a SQLAlchemy query that returns ORM model instances.

        Args:
            query: The main query to paginate (should select model instances)
            params: Pagination parameters
            count_query: Optional custom count query (defaults to counting main query)

        Returns:
            Tuple of (model_instances, total_count)
        """
        # Get total count
        if count_query is None:
            # Create count query from main query
            count_query = select(func.count()).select_from(query.subquery())

        total_result = await self.session.execute(count_query)
        total = total_result.scalar() or 0

        # Apply pagination to main query
        paginated_query = query.limit(params.limit).offset(params.offset)
        items_result = await self.session.execute(paginated_query)
        items = items_result.scalars().all()

        return items, total

    async def paginate_response(
        self,
        query: Select,
        params: PaginationParams,
        count_query: Optional[Select] = None
    ) -> PaginatedResponse[Dict[str, Any]]:
        """
        Paginate a query and return a PaginatedResponse.

        Args:
            query: The main query to paginate
            params: Pagination parameters
            count_query: Optional custom count query

        Returns:
            PaginatedResponse with items and metadata
        """
        items, total = await self.paginate(query, params, count_query)
        return PaginatedResponse.create(items, total, params)


# FastAPI dependency for pagination parameters
def get_pagination_params(
    page: int = Query(1, ge=1, description="Page number (1-based)"),
    limit: int = Query(
        10, ge=1, le=100, description="Number of items per page")
) -> PaginationParams:
    """FastAPI dependency to get pagination parameters from query string."""
    return PaginationParams(page=page, limit=limit)


# Utility functions for common pagination patterns
def create_paginated_response(
    items: List[T],
    total: int,
    page: int,
    limit: int
) -> PaginatedResponse[T]:
    """Create a paginated response from raw parameters."""
    params = PaginationParams(page=page, limit=limit)
    return PaginatedResponse.create(items, total, params)


def get_pagination_info(total: int, page: int, limit: int) -> Dict[str, Any]:
    """Get pagination information as a dictionary."""
    pages = ceil(total / limit) if total > 0 else 1
    has_prev = page > 1
    has_next = page < pages

    return {
        "page": page,
        "limit": limit,
        "total": total,
        "pages": pages,
        "has_prev": has_prev,
        "has_next": has_next,
        "prev_page": page - 1 if has_prev else None,
        "next_page": page + 1 if has_next else None,
    }


# Common pagination queries
class PaginationQueries:
    """Common pagination query patterns."""

    @staticmethod
    def add_pagination(query: Select, params: PaginationParams) -> Select:
        """Add pagination to a SQLAlchemy query."""
        return query.limit(params.limit).offset(params.offset)

    @staticmethod
    def create_count_query(base_query: Select) -> Select:
        """Create a count query from a base query."""
        return select(func.count()).select_from(base_query.subquery())

    @staticmethod
    def add_ordering(query: Select, order_by: str, desc: bool = False) -> Select:
        """Add ordering to a query."""
        from sqlalchemy import desc as sql_desc, asc

        # This is a simplified version - in practice you'd want to validate the column
        if desc:
            return query.order_by(sql_desc(order_by))
        else:
            return query.order_by(asc(order_by))


# Search and filter utilities that work with pagination
class SearchParams(CustomModel):
    """Search parameters that can be combined with pagination."""

    q: Optional[str] = Field(None, description="Search query")
    sort_by: Optional[str] = Field(None, description="Field to sort by")
    sort_order: str = Field(default="asc", description="Sort order (asc/desc)")

    @field_validator("sort_order")
    def validate_sort_order(cls, v):
        if v.lower() not in ["asc", "desc"]:
            raise ValueError("Sort order must be 'asc' or 'desc'")
        return v.lower()


def get_search_params(
    q: Optional[str] = Query(None, description="Search query"),
    sort_by: Optional[str] = Query(None, description="Field to sort by"),
    sort_order: str = Query("asc", description="Sort order (asc/desc)")
) -> SearchParams:
    """FastAPI dependency to get search parameters."""
    return SearchParams(q=q, sort_by=sort_by, sort_order=sort_order)


# Combined pagination and search dependency
class PaginationAndSearchParams(CustomModel):
    """Combined pagination and search parameters."""

    pagination: PaginationParams
    search: SearchParams

    @classmethod
    def from_params(
        cls,
        pagination: PaginationParams,
        search: SearchParams
    ) -> "PaginationAndSearchParams":
        """Create from separate parameter objects."""
        return cls(pagination=pagination, search=search)


def get_pagination_and_search_params(
    pagination: PaginationParams = Query(get_pagination_params),
    search: SearchParams = Query(get_search_params)
) -> PaginationAndSearchParams:
    """FastAPI dependency for combined pagination and search."""
    return PaginationAndSearchParams.from_params(pagination, search)
