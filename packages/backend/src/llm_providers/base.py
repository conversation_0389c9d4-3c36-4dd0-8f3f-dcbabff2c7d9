"""
Base classes for LLM provider clients.
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from src.constants import LL<PERSON>rovider


@dataclass
class LLMResponse:
    """Standard response format for LLM providers."""
    
    content: str
    provider: <PERSON>MProvider
    model: str
    usage: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    raw_response: Optional[Dict[str, Any]] = None


@dataclass
class LLMMessage:
    """Message format for LLM conversations."""
    
    role: str  # "user", "assistant", "system"
    content: str
    metadata: Optional[Dict[str, Any]] = None


class BaseLLMClient(ABC):
    """Abstract base class for LLM provider clients."""
    
    def __init__(self, api_key: str, **kwargs):
        self.api_key = api_key
        self.config = kwargs
    
    @abstractmethod
    async def generate_text(
        self,
        messages: List[LLMMessage],
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        **kwargs
    ) -> LLMResponse:
        """Generate text using the LLM provider."""
        pass
    
    @abstractmethod
    async def generate_text_with_image(
        self,
        messages: List[LLMMessage],
        image_data: bytes,
        image_format: str,
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        **kwargs
    ) -> LLMResponse:
        """Generate text with image input using the LLM provider."""
        pass
    
    @abstractmethod
    def get_provider(self) -> LLMProvider:
        """Get the provider type."""
        pass
    
    @abstractmethod
    def get_default_model(self) -> str:
        """Get the default model for this provider."""
        pass
    
    @abstractmethod
    def get_available_models(self) -> List[str]:
        """Get list of available models for this provider."""
        pass
    
    def validate_api_key(self) -> bool:
        """Validate the API key format."""
        return bool(self.api_key and len(self.api_key.strip()) > 0)
    
    def prepare_messages(self, messages: List[LLMMessage]) -> List[Dict[str, Any]]:
        """Prepare messages for the provider's format."""
        return [
            {
                "role": msg.role,
                "content": msg.content,
                **(msg.metadata or {})
            }
            for msg in messages
        ]


class LLMClientError(Exception):
    """Base exception for LLM client errors."""
    
    def __init__(self, message: str, provider: LLMProvider, error_code: Optional[str] = None):
        super().__init__(message)
        self.provider = provider
        self.error_code = error_code


class LLMAPIError(LLMClientError):
    """Exception for LLM API errors."""
    pass


class LLMRateLimitError(LLMClientError):
    """Exception for LLM rate limit errors."""
    pass


class LLMAuthenticationError(LLMClientError):
    """Exception for LLM authentication errors."""
    pass


class LLMValidationError(LLMClientError):
    """Exception for LLM validation errors."""
    pass
