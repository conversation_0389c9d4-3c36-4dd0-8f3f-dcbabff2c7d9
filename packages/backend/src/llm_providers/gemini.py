"""
Google Gemini LLM provider client.
"""
import base64
from typing import List, Optional, Dict, Any

import httpx

from src.constants import LLMProvider
from src.llm_providers.base import (
    BaseLLMClient, LLMResponse, LLMMessage,
    LLMAPIError, LLMRateLimitError, LLMAuthenticationError, LLMValidationError
)


class GeminiClient(BaseLLMClient):
    """Google Gemini API client."""
    
    BASE_URL = "https://generativelanguage.googleapis.com/v1beta"
    DEFAULT_MODEL = "gemini-1.5-pro"
    
    AVAILABLE_MODELS = [
        "gemini-1.5-pro",
        "gemini-1.5-flash",
        "gemini-1.0-pro"
    ]
    
    def __init__(self, api_key: str, **kwargs):
        super().__init__(api_key, **kwargs)
        self.client = httpx.AsyncClient(
            base_url=self.BASE_URL,
            timeout=60.0
        )
    
    async def generate_text(
        self,
        messages: List[LLMMessage],
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        **kwargs
    ) -> LLMResponse:
        """Generate text using Gemini."""
        model = model or self.DEFAULT_MODEL
        temperature = temperature or 0.7
        
        # Prepare messages for Gemini format
        contents = []
        system_instruction = None
        
        for msg in messages:
            if msg.role == "system":
                system_instruction = {"parts": [{"text": msg.content}]}
            else:
                role = "user" if msg.role == "user" else "model"
                contents.append({
                    "role": role,
                    "parts": [{"text": msg.content}]
                })
        
        payload = {
            "contents": contents,
            "generationConfig": {
                "temperature": temperature,
                "candidateCount": 1,
                **kwargs
            }
        }
        
        if max_tokens:
            payload["generationConfig"]["maxOutputTokens"] = max_tokens
        
        if system_instruction:
            payload["systemInstruction"] = system_instruction
        
        try:
            url = f"/models/{model}:generateContent?key={self.api_key}"
            response = await self.client.post(url, json=payload)
            response.raise_for_status()
            
            data = response.json()
            candidate = data["candidates"][0]
            content = candidate["content"]["parts"][0]["text"]
            
            return LLMResponse(
                content=content,
                provider=LLMProvider.GEMINI,
                model=model,
                usage=data.get("usageMetadata"),
                metadata={
                    "finish_reason": candidate.get("finishReason"),
                    "safety_ratings": candidate.get("safetyRatings")
                },
                raw_response=data
            )
            
        except httpx.HTTPStatusError as e:
            await self._handle_http_error(e)
        except Exception as e:
            raise LLMAPIError(f"Gemini API error: {str(e)}", LLMProvider.GEMINI)
    
    async def generate_text_with_image(
        self,
        messages: List[LLMMessage],
        image_data: bytes,
        image_format: str,
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        **kwargs
    ) -> LLMResponse:
        """Generate text with image input using Gemini."""
        model = model or self.DEFAULT_MODEL
        temperature = temperature or 0.7
        
        # Encode image to base64
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        
        # Prepare messages with image
        contents = []
        system_instruction = None
        
        for msg in messages:
            if msg.role == "system":
                system_instruction = {"parts": [{"text": msg.content}]}
            elif msg.role == "user":
                # Add image to the first user message
                contents.append({
                    "role": "user",
                    "parts": [
                        {"text": msg.content},
                        {
                            "inlineData": {
                                "mimeType": f"image/{image_format}",
                                "data": image_base64
                            }
                        }
                    ]
                })
            else:
                role = "model"
                contents.append({
                    "role": role,
                    "parts": [{"text": msg.content}]
                })
        
        payload = {
            "contents": contents,
            "generationConfig": {
                "temperature": temperature,
                "candidateCount": 1,
                **kwargs
            }
        }
        
        if max_tokens:
            payload["generationConfig"]["maxOutputTokens"] = max_tokens
        
        if system_instruction:
            payload["systemInstruction"] = system_instruction
        
        try:
            url = f"/models/{model}:generateContent?key={self.api_key}"
            response = await self.client.post(url, json=payload)
            response.raise_for_status()
            
            data = response.json()
            candidate = data["candidates"][0]
            content = candidate["content"]["parts"][0]["text"]
            
            return LLMResponse(
                content=content,
                provider=LLMProvider.GEMINI,
                model=model,
                usage=data.get("usageMetadata"),
                metadata={
                    "finish_reason": candidate.get("finishReason"),
                    "safety_ratings": candidate.get("safetyRatings"),
                    "image_format": image_format
                },
                raw_response=data
            )
            
        except httpx.HTTPStatusError as e:
            await self._handle_http_error(e)
        except Exception as e:
            raise LLMAPIError(f"Gemini API error: {str(e)}", LLMProvider.GEMINI)
    
    async def _handle_http_error(self, error: httpx.HTTPStatusError):
        """Handle HTTP errors from Gemini API."""
        status_code = error.response.status_code
        
        try:
            error_data = error.response.json()
            error_message = error_data.get("error", {}).get("message", str(error))
            error_code = error_data.get("error", {}).get("code", "unknown")
        except:
            error_message = str(error)
            error_code = "unknown"
        
        if status_code == 401 or status_code == 403:
            raise LLMAuthenticationError(f"Gemini authentication error: {error_message}", LLMProvider.GEMINI)
        elif status_code == 429:
            raise LLMRateLimitError(f"Gemini rate limit exceeded: {error_message}", LLMProvider.GEMINI)
        elif status_code == 400:
            raise LLMValidationError(f"Gemini validation error: {error_message}", LLMProvider.GEMINI)
        else:
            raise LLMAPIError(f"Gemini API error ({status_code}): {error_message}", LLMProvider.GEMINI, str(error_code))
    
    def get_provider(self) -> LLMProvider:
        """Get the provider type."""
        return LLMProvider.GEMINI
    
    def get_default_model(self) -> str:
        """Get the default model for Gemini."""
        return self.DEFAULT_MODEL
    
    def get_available_models(self) -> List[str]:
        """Get list of available Gemini models."""
        return self.AVAILABLE_MODELS.copy()
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
