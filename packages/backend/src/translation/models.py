"""
SQLAlchemy models for translation processing.
"""
from typing import List, Optional

from sqlalchemy import String, Text, Float, Integer, ForeignKey, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.constants import TranslationStatus, LLMProvider
from src.models import BaseModel


class TranslationJob(BaseModel):
    """Translation job model for tracking translation tasks."""

    __tablename__ = "translation_job"

    text_region_id: Mapped[str] = mapped_column(
        String(36), ForeignKey("text_region.id"))
    status: Mapped[str] = mapped_column(
        String(50), default=TranslationStatus.PENDING)
    provider: Mapped[str] = mapped_column(
        String(50), default=LLMProvider.CLAUDE)

    # Languages
    source_language: Mapped[str] = mapped_column(String(50))
    target_language: Mapped[str] = mapped_column(String(50))

    # Input and output
    original_text: Mapped[str] = mapped_column(Text)
    translated_text: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Processing details
    prompt_used: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    raw_response: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    processing_time_seconds: Mapped[Optional[float]
                                    ] = mapped_column(Float, nullable=True)

    # Quality metrics
    confidence_score: Mapped[Optional[float]
                             ] = mapped_column(Float, nullable=True)
    quality_score: Mapped[Optional[float]
                          ] = mapped_column(Float, nullable=True)

    # Error handling
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    retry_count: Mapped[int] = mapped_column(Integer, default=0)

    # Additional metadata
    extra_data: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)

    # Relationships
    text_region: Mapped["TextRegion"] = relationship(
        foreign_keys="TranslationJob.text_region_id")
    alternatives: Mapped[List["TranslationAlternative"]] = relationship(
        back_populates="job", cascade="all, delete-orphan")


class TranslationAlternative(BaseModel):
    """Translation alternative model for storing multiple translation options."""

    __tablename__ = "translation_alternative"

    job_id: Mapped[str] = mapped_column(
        String(36), ForeignKey("translation_job.id"))

    # Translation content
    translated_text: Mapped[str] = mapped_column(Text)
    confidence_score: Mapped[Optional[float]
                             ] = mapped_column(Float, nullable=True)
    quality_score: Mapped[Optional[float]
                          ] = mapped_column(Float, nullable=True)

    # Ranking and selection
    rank: Mapped[int] = mapped_column(Integer)
    # Using string for SQLite compatibility
    is_selected: Mapped[str] = mapped_column(String(5), default="false")

    # Additional metadata
    extra_data: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)

    # Relationships
    job: Mapped["TranslationJob"] = relationship(back_populates="alternatives")


class TranslationTemplate(BaseModel):
    """Translation template model for storing reusable translation patterns."""

    __tablename__ = "translation_template"

    name: Mapped[str] = mapped_column(String(255))
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Languages
    source_language: Mapped[str] = mapped_column(String(50))
    target_language: Mapped[str] = mapped_column(String(50))

    # Template content
    source_pattern: Mapped[str] = mapped_column(Text)
    target_pattern: Mapped[str] = mapped_column(Text)

    # Usage statistics
    usage_count: Mapped[int] = mapped_column(Integer, default=0)

    # Categorization
    category: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    tags: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)

    # Quality metrics
    average_quality_score: Mapped[Optional[float]
                                  ] = mapped_column(Float, nullable=True)
