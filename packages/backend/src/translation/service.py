"""
Business logic for translation processing.
"""
from typing import List, Optional

from sqlalchemy import select, update, delete, func, and_
from sqlalchemy.ext.asyncio import AsyncSession

from src.translation.models import TranslationJob, TranslationAlternative, TranslationTemplate
from src.translation.schemas import (
    TranslationJobCreate, TranslationJobResponse, TranslationJobDetailResponse,
    TranslationAlternativeCreate, TranslationAlternativeResponse,
    TranslationTemplateCreate, TranslationTemplateUpdate, TranslationTemplateResponse,
    TranslationProcessRequest, TranslationBatchProcessRequest,
    TranslationStatistics, LLMTranslationResponse
)
from src.translation.exceptions import TranslationJobNotFound, TranslationTemplateNotFound, TranslationFailed
from src.constants import TranslationStatus, LLMProvider, DEFAULT_TRANSLATION_PROMPT
from src.config import settings
from src.pagination import PaginationParams, PaginatedResponse, Paginator


class TranslationService:
    """Service class for translation processing operations."""

    def __init__(self, session: AsyncSession):
        self.session = session
        self.paginator = Paginator(session)

    async def create_translation_job(self, job_data: TranslationJobCreate) -> TranslationJobResponse:
        """Create a new translation job."""
        # Build prompt
        prompt = job_data.custom_prompt or DEFAULT_TRANSLATION_PROMPT.format(
            source_language=job_data.source_language,
            target_language=job_data.target_language,
            text=job_data.original_text,
            text_type="unknown"  # TODO: Get from text region
        )

        # Create ORM model instance - timestamps will be set automatically
        translation_job = TranslationJob(
            text_region_id=job_data.text_region_id,
            provider=job_data.provider,
            source_language=job_data.source_language,
            target_language=job_data.target_language,
            original_text=job_data.original_text,
            prompt_used=prompt,
            status=TranslationStatus.PENDING
        )

        self.session.add(translation_job)
        await self.session.commit()
        await self.session.refresh(translation_job)

        return await self.get_translation_job(translation_job.id)

    async def get_translation_job(self, job_id: str) -> TranslationJobResponse:
        """Get a translation job by ID."""
        query = select(TranslationJob).where(TranslationJob.id == job_id)
        result = await self.session.execute(query)
        translation_job = result.scalar_one_or_none()

        if not translation_job:
            raise TranslationJobNotFound(
                f"Translation job with ID {job_id} not found")

        return TranslationJobResponse(**translation_job.to_dict())

    async def get_translation_jobs_by_region(self, text_region_id: str) -> List[TranslationJobResponse]:
        """Get all translation jobs for a text region."""
        query = select(TranslationJob).where(
            TranslationJob.text_region_id == text_region_id
        ).order_by(TranslationJob.created_at.desc())

        result = await self.session.execute(query)
        translation_jobs = result.scalars().all()
        return [TranslationJobResponse(**translation_job.to_dict()) for translation_job in translation_jobs]

    async def get_translation_job_detail(self, job_id: str) -> TranslationJobDetailResponse:
        """Get detailed translation job information with alternatives."""
        job = await self.get_translation_job(job_id)
        alternatives = await self.get_translation_alternatives(job_id)

        return TranslationJobDetailResponse(
            **job.model_dump(),
            alternatives=alternatives
        )

    async def get_translation_alternatives(self, job_id: str) -> List[TranslationAlternativeResponse]:
        """Get all translation alternatives for a job."""
        query = select(TranslationAlternative).where(
            TranslationAlternative.job_id == job_id
        ).order_by(TranslationAlternative.rank)

        result = await self.session.execute(query)
        translation_alternatives = result.scalars().all()
        alternatives = []

        for alternative_model in translation_alternatives:
            alternative_dict = alternative_model.to_dict()
            # Convert string to boolean for is_selected
            alternative_dict["is_selected"] = alternative_model.is_selected == "true"
            alternative = TranslationAlternativeResponse(**alternative_dict)
            alternatives.append(alternative)

        return alternatives

    async def update_translation_job_status(
        self,
        job_id: str,
        status: TranslationStatus,
        translated_text: Optional[str] = None,
        error_message: Optional[str] = None,
        processing_time: Optional[float] = None,
        confidence_score: Optional[float] = None,
        quality_score: Optional[float] = None
    ) -> TranslationJobResponse:
        """Update translation job status and results."""
        update_data = {"status": status}

        if translated_text:
            update_data["translated_text"] = translated_text

        if error_message:
            update_data["error_message"] = error_message

        if processing_time:
            update_data["processing_time_seconds"] = processing_time

        if confidence_score:
            update_data["confidence_score"] = confidence_score

        if quality_score:
            update_data["quality_score"] = quality_score

        query = update(TranslationJob).where(
            TranslationJob.id == job_id).values(**update_data)
        await self.session.execute(query)
        await self.session.commit()

        return await self.get_translation_job(job_id)

    async def save_translation_alternatives(
        self,
        job_id: str,
        alternatives: List[TranslationAlternativeCreate]
    ) -> List[TranslationAlternativeResponse]:
        """Save translation alternatives for a job."""
        saved_alternatives = []

        for alt_data in alternatives:
            # Create ORM model instance - timestamps will be set automatically
            translation_alt = TranslationAlternative(
                job_id=job_id,
                translated_text=alt_data.translated_text,
                confidence_score=alt_data.confidence_score,
                quality_score=alt_data.quality_score,
                rank=alt_data.rank,
                is_selected="false",  # Default to not selected
                extra_data=alt_data.metadata  # Note: renamed from metadata to extra_data
            )

            self.session.add(translation_alt)
            await self.session.commit()
            await self.session.refresh(translation_alt)

            # Convert to response schema
            alternative = TranslationAlternativeResponse(
                id=translation_alt.id,
                job_id=translation_alt.job_id,
                translated_text=translation_alt.translated_text,
                confidence_score=translation_alt.confidence_score,
                quality_score=translation_alt.quality_score,
                rank=translation_alt.rank,
                is_selected=False,  # Convert string to boolean
                metadata=translation_alt.extra_data,  # Map back to metadata for response
                created_at=translation_alt.created_at,
                updated_at=translation_alt.updated_at
            )
            saved_alternatives.append(alternative)

        return saved_alternatives

    async def select_translation_alternative(self, job_id: str, alternative_id: str) -> TranslationJobResponse:
        """Select a translation alternative as the primary translation."""
        # First, deselect all alternatives for this job
        deselect_query = update(TranslationAlternative).where(
            TranslationAlternative.job_id == job_id
        ).values(is_selected="false")
        await self.session.execute(deselect_query)
        await self.session.commit()

        # Select the specified alternative
        select_query = update(TranslationAlternative).where(
            and_(
                TranslationAlternative.job_id == job_id,
                TranslationAlternative.id == alternative_id
            )
        ).values(is_selected="true")
        await self.session.execute(select_query)
        await self.session.commit()

        # Get the selected alternative text and update the job
        alt_query = select(TranslationAlternative).where(
            TranslationAlternative.id == alternative_id)
        alt_result = await self.session.execute(alt_query)
        alternative = alt_result.scalar_one_or_none()

        if alternative:
            job_update_query = update(TranslationJob).where(
                TranslationJob.id == job_id
            ).values(translated_text=alternative.translated_text)
            await self.session.execute(job_update_query)
            await self.session.commit()

        return await self.get_translation_job(job_id)

    async def process_translation_request(self, request: TranslationProcessRequest) -> TranslationJobResponse:
        """Process a translation request."""
        # Get original text from request or text region
        original_text = request.original_text

        # If no original text provided, we'll fetch it in the background task
        # This allows for batch processing where text is fetched per region
        if not original_text:
            original_text = "pending"  # Placeholder, will be updated in background task

        # Create translation job
        job_data = TranslationJobCreate(
            text_region_id=request.text_region_id,
            provider=request.provider or LLMProvider.CLAUDE,
            source_language=request.source_language or settings.SOURCE_LANGUAGE,
            target_language=request.target_language or settings.TARGET_LANGUAGE,
            original_text=original_text,
            custom_prompt=request.custom_prompt
        )

        job = await self.create_translation_job(job_data)

        # Job will be processed in background task
        return job

    # Translation Template methods
    async def create_translation_template(self, template_data: TranslationTemplateCreate) -> TranslationTemplateResponse:
        """Create a new translation template."""
        # Create ORM model instance - timestamps will be set automatically
        translation_template = TranslationTemplate(
            name=template_data.name,
            description=template_data.description,
            source_language=template_data.source_language,
            target_language=template_data.target_language,
            source_pattern=template_data.source_pattern,
            target_pattern=template_data.target_pattern,
            category=template_data.category,
            tags=template_data.tags
        )

        self.session.add(translation_template)
        await self.session.commit()
        await self.session.refresh(translation_template)

        return await self.get_translation_template(translation_template.id)

    async def get_translation_template(self, template_id: str) -> TranslationTemplateResponse:
        """Get a translation template by ID."""
        query = select(TranslationTemplate).where(
            TranslationTemplate.id == template_id)
        result = await self.session.execute(query)
        translation_template = result.scalar_one_or_none()

        if not translation_template:
            raise TranslationTemplateNotFound(
                f"Translation template with ID {template_id} not found")

        return TranslationTemplateResponse(**translation_template.to_dict())

    async def get_translation_templates(
        self,
        source_language: Optional[str] = None,
        target_language: Optional[str] = None,
        category: Optional[str] = None,
        limit: int = 10,
        offset: int = 0
    ) -> List[TranslationTemplateResponse]:
        """Get translation templates with filtering."""
        query = select(TranslationTemplate)

        if source_language:
            query = query.where(
                TranslationTemplate.source_language == source_language)

        if target_language:
            query = query.where(
                TranslationTemplate.target_language == target_language)

        if category:
            query = query.where(TranslationTemplate.category == category)

        query = query.order_by(TranslationTemplate.usage_count.desc()).limit(
            limit).offset(offset)

        result = await self.session.execute(query)
        translation_templates = result.scalars().all()
        return [TranslationTemplateResponse(**template.to_dict()) for template in translation_templates]

    async def get_translation_templates_paginated(
        self,
        source_language: Optional[str] = None,
        target_language: Optional[str] = None,
        category: Optional[str] = None,
        params: PaginationParams = None
    ) -> PaginatedResponse[TranslationTemplateResponse]:
        """Get translation templates with pagination."""
        query = select(TranslationTemplate)

        if source_language:
            query = query.where(
                TranslationTemplate.source_language == source_language)

        if target_language:
            query = query.where(
                TranslationTemplate.target_language == target_language)

        if category:
            query = query.where(TranslationTemplate.category == category)

        query = query.order_by(TranslationTemplate.usage_count.desc())

        items, total = await self.paginator.paginate(query, params)
        template_responses = [
            TranslationTemplateResponse(**item) for item in items]

        return PaginatedResponse.create(template_responses, total, params)

    async def update_translation_template(
        self,
        template_id: str,
        template_data: TranslationTemplateUpdate
    ) -> TranslationTemplateResponse:
        """Update a translation template."""
        # Check if template exists
        await self.get_translation_template(template_id)

        # Build update data
        update_data = template_data.model_dump(exclude_unset=True)
        if not update_data:
            return await self.get_translation_template(template_id)

        query = update(TranslationTemplate).where(
            TranslationTemplate.id == template_id).values(**update_data)
        await self.session.execute(query)
        await self.session.commit()

        return await self.get_translation_template(template_id)

    async def delete_translation_template(self, template_id: str) -> bool:
        """Delete a translation template."""
        # Check if template exists
        await self.get_translation_template(template_id)

        query = delete(TranslationTemplate).where(
            TranslationTemplate.id == template_id)
        await self.session.execute(query)
        await self.session.commit()
        return True

    async def get_translation_statistics(self, project_id: Optional[str] = None) -> TranslationStatistics:
        """Get translation processing statistics."""
        base_query = select(TranslationJob)

        if project_id:
            # Join with TextRegion and ProjectPage to filter by project
            from src.projects.models import TextRegion, ProjectPage
            base_query = base_query.join(TextRegion).join(
                ProjectPage).where(ProjectPage.project_id == project_id)

        # Count jobs by status
        status_counts = {}
        for status in TranslationStatus:
            count_query = base_query.where(TranslationJob.status == status)
            count = await self.session.scalar(select(func.count()).select_from(count_query.subquery()))
            status_counts[status] = count or 0

        # Calculate averages
        avg_time_query = select(func.avg(
            TranslationJob.processing_time_seconds)).select_from(base_query.subquery())
        avg_confidence_query = select(
            func.avg(TranslationJob.confidence_score)).select_from(base_query.subquery())
        avg_quality_query = select(
            func.avg(TranslationJob.quality_score)).select_from(base_query.subquery())

        avg_time = await self.session.scalar(avg_time_query)
        avg_confidence = await self.session.scalar(avg_confidence_query)
        avg_quality = await self.session.scalar(avg_quality_query)

        # Get language pair statistics
        lang_pair_query = select(
            func.concat(TranslationJob.source_language, " -> ",
                        TranslationJob.target_language).label("pair"),
            func.count().label("count")
        ).select_from(base_query.subquery()).group_by("pair")

        lang_pairs_result = await self.session.execute(lang_pair_query)
        lang_pairs = lang_pairs_result.fetchall()
        language_pairs = {pair.pair: pair.count for pair in lang_pairs}

        return TranslationStatistics(
            total_jobs=sum(status_counts.values()),
            completed_jobs=status_counts.get(TranslationStatus.COMPLETED, 0),
            failed_jobs=status_counts.get(TranslationStatus.FAILED, 0),
            pending_jobs=status_counts.get(TranslationStatus.PENDING, 0),
            processing_jobs=status_counts.get(
                TranslationStatus.IN_PROGRESS, 0),
            average_processing_time=avg_time,
            average_confidence_score=avg_confidence,
            average_quality_score=avg_quality,
            language_pairs=language_pairs
        )
