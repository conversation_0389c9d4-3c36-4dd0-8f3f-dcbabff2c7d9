"""
Global configuration settings for ho-trans application.
"""
from typing import List

from pydantic import Field
from pydantic_settings import BaseSettings

from src.constants import Environment


class Config(BaseSettings):
    """Global application configuration."""

    # Database
    DATABASE_URL: str = Field(
        default="sqlite:///./ho_trans.db",
        description="SQLite database URL"
    )

    # Environment
    ENVIRONMENT: Environment = Field(
        default=Environment.DEVELOPMENT,
        description="Application environment"
    )

    # API Documentation
    SHOW_DOCS_ENVIRONMENT: tuple[str, ...] = ("development", "staging")

    # CORS
    CORS_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        description="Allowed CORS origins"
    )

    # LLM Providers
    ANTHROPIC_API_KEY: str = Field(
        default="",
        description="Anthropic Claude API key"
    )

    OPENAI_API_KEY: str = Field(
        default="",
        description="OpenAI API key"
    )

    GOOGLE_API_KEY: str = Field(
        default="",
        description="Google Gemini API key"
    )

    DEEPSEEK_API_KEY: str = Field(
        default="",
        description="Deepseek API key"
    )

    # OCR Settings
    DEFAULT_OCR_PROVIDER: str = Field(
        default="claude",
        description="Default LLM provider for OCR"
    )

    # Translation Settings
    DEFAULT_TRANSLATION_PROVIDER: str = Field(
        default="claude",
        description="Default LLM provider for translation"
    )

    SOURCE_LANGUAGE: str = Field(
        default="japanese",
        description="Default source language for translation"
    )

    TARGET_LANGUAGE: str = Field(
        default="indonesian",
        description="Default target language for translation"
    )

    # File Upload
    MAX_FILE_SIZE: int = Field(
        default=10 * 1024 * 1024,  # 10MB
        description="Maximum file size for uploads in bytes"
    )

    ALLOWED_IMAGE_TYPES: List[str] = Field(
        default=["image/jpeg", "image/png", "image/webp"],
        description="Allowed image MIME types"
    )

    # Storage
    UPLOAD_DIR: str = Field(
        default="./uploads",
        description="Directory for uploaded files"
    )

    # Logging
    LOG_LEVEL: str = Field(
        default="INFO",
        description="Logging level"
    )

    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Config()
