"""
Project-specific exceptions.
"""
from src.constants import ErrorCode
from src.exceptions import NotFoundError, BadRequestError, ConflictError


class ProjectNotFound(NotFoundError):
    """Project not found exception."""
    
    def __init__(self, detail: str = "Project not found"):
        super().__init__(detail=detail, error_code=ErrorCode.PROJECT_NOT_FOUND)


class ProjectAlreadyExists(ConflictError):
    """Project already exists exception."""
    
    def __init__(self, detail: str = "Project already exists"):
        super().__init__(detail=detail, error_code=ErrorCode.PROJECT_ALREADY_EXISTS)


class ProjectPageNotFound(NotFoundError):
    """Project page not found exception."""
    
    def __init__(self, detail: str = "Project page not found"):
        super().__init__(detail=detail, error_code="PROJECT_PAGE_NOT_FOUND")


class TextRegionNotFound(NotFoundError):
    """Text region not found exception."""
    
    def __init__(self, detail: str = "Text region not found"):
        super().__init__(detail=detail, error_code="TEXT_REGION_NOT_FOUND")


class InvalidPageNumber(BadRequestError):
    """Invalid page number exception."""
    
    def __init__(self, detail: str = "Invalid page number"):
        super().__init__(detail=detail, error_code="INVALID_PAGE_NUMBER")


class InvalidTextRegionCoordinates(BadRequestError):
    """Invalid text region coordinates exception."""
    
    def __init__(self, detail: str = "Invalid text region coordinates"):
        super().__init__(detail=detail, error_code="INVALID_TEXT_REGION_COORDINATES")
