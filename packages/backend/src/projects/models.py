"""
SQLAlchemy models for project management.
"""
from typing import List, Optional

from sqlalchemy import String, Text, Integer, ForeignKey, Float
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.constants import ProjectStatus, TextRegionType, OCRStatus, TranslationStatus
from src.models import BaseModel


class Project(BaseModel):
    """Project model for manga translation projects."""

    __tablename__ = "project"

    name: Mapped[str] = mapped_column(String(255), index=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    status: Mapped[str] = mapped_column(
        String(50), default=ProjectStatus.DRAFT)
    source_language: Mapped[str] = mapped_column(
        String(50), default="japanese")
    target_language: Mapped[str] = mapped_column(String(50), default="english")

    # Relationships
    pages: Mapped[List["ProjectPage"]] = relationship(
        back_populates="project", cascade="all, delete-orphan")


class ProjectPage(BaseModel):
    """Project page model for individual manga pages."""

    __tablename__ = "project_page"

    project_id: Mapped[str] = mapped_column(
        String(36), ForeignKey("project.id"))
    page_number: Mapped[int] = mapped_column(Integer)
    original_filename: Mapped[str] = mapped_column(String(255))
    file_path: Mapped[str] = mapped_column(String(500))
    file_size: Mapped[int] = mapped_column(Integer)
    image_width: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    image_height: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    ocr_status: Mapped[str] = mapped_column(
        String(50), default=OCRStatus.PENDING)

    # Relationships
    project: Mapped["Project"] = relationship(back_populates="pages")
    text_regions: Mapped[List["TextRegion"]] = relationship(
        back_populates="page", cascade="all, delete-orphan")


class TextRegion(BaseModel):
    """Text region model for detected text areas in manga pages."""

    __tablename__ = "text_region"

    page_id: Mapped[str] = mapped_column(
        String(36), ForeignKey("project_page.id"))
    region_type: Mapped[str] = mapped_column(
        String(50), default=TextRegionType.SPEECH_BUBBLE)

    # Bounding box coordinates (normalized 0-1)
    x: Mapped[float] = mapped_column(Float)
    y: Mapped[float] = mapped_column(Float)
    width: Mapped[float] = mapped_column(Float)
    height: Mapped[float] = mapped_column(Float)

    # OCR results
    original_text: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    confidence_score: Mapped[Optional[float]
                             ] = mapped_column(Float, nullable=True)

    # Translation
    translated_text: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    translation_status: Mapped[str] = mapped_column(
        String(50), default=TranslationStatus.PENDING)

    # Styling information for frontend
    font_family: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True)
    font_size: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    font_color: Mapped[Optional[str]] = mapped_column(
        String(7), nullable=True)  # Hex color
    background_color: Mapped[Optional[str]] = mapped_column(
        String(11), nullable=True)  # Hex color or 'transparent'
    background_opacity: Mapped[Optional[float]] = mapped_column(
        Float, nullable=True, default=1.0)  # Opacity 0.0-1.0

    # Relationships
    page: Mapped["ProjectPage"] = relationship(back_populates="text_regions")
