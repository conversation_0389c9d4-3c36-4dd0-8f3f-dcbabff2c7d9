# ho-trans Backend

FastAPI backend for the ho-trans manga translation tool with LLM-based OCR and translation capabilities.

## Features

- **Project Management**: Create and manage manga translation projects
- **LLM-based OCR**: Extract text from manga images using Claude, GPT-4, or Gemini
- **Multi-provider Translation**: Translate text using multiple LLM providers
- **Text Region Management**: Define and manage text regions within manga pages
- **SQLite Database**: Local-only storage for translation projects
- **RESTful API**: Clean, documented API endpoints

## Architecture

The backend follows a domain-driven design with standardized ORM patterns and service layer architecture:

### Domain Modules

- `projects/` - Project and page management
- `ocr/` - LLM-based text detection and extraction
- `translation/` - Text translation services
- `llm_providers/` - LLM provider integrations (Claude, OpenAI, Gemini)

### Service Layer Patterns

- **Consistent ORM Usage**: All services use standardized SQLAlchemy ORM patterns
- **Single Entity Retrieval**: `scalar_one_or_none()` for individual records
- **Multiple Entity Retrieval**: `scalars().all()` for collections
- **Efficient Aggregation**: Separate queries for computed fields like counts
- **Pagination Support**: Both ORM and explicit column selection patterns

## Quick Start

### Prerequisites

- Python 3.11+
- pip or poetry

### Installation

1. Clone the repository and navigate to the backend directory:
```bash
cd packages/backend
```

2. Install dependencies:
```bash
make install
# or
pip install -r requirements/dev.txt
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your API keys
```

4. Initialize the database:
```bash
make init-db
```

5. Seed with sample data (optional):
```bash
make seed-db
```

6. Start the development server:
```bash
make server
```

The API will be available at `http://localhost:8000` with interactive documentation at `http://localhost:8000/docs`.

## Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure:

- `ANTHROPIC_API_KEY` - Anthropic Claude API key
- `OPENAI_API_KEY` - OpenAI API key  
- `GOOGLE_API_KEY` - Google Gemini API key
- `DATABASE_URL` - SQLite database path
- `CORS_ORIGINS` - Allowed CORS origins for frontend

### LLM Providers

The application supports multiple LLM providers:

- **Claude** (Anthropic) - Recommended for OCR and translation
- **GPT-4** (OpenAI) - Good for translation tasks
- **Gemini** (Google) - Alternative provider

At least one provider must be configured with a valid API key.

## Development

### Available Commands

```bash
make help           # Show available commands
make install        # Install dependencies
make server         # Run development server
make test           # Run tests
make lint           # Run code linting
make format         # Format code
make typecheck      # Run type checking
make init-db        # Initialize database
make reset-db       # Reset database
make check-db       # Check database status
make seed-db        # Seed with sample data
make migrate        # Run migrations
make clean          # Clean up generated files
```

### Database Management

The application uses SQLite with Alembic for migrations:

```bash
# Initialize database
make init-db

# Create new migration
alembic revision --autogenerate -m "description"

# Run migrations
make migrate

# Reset database (WARNING: destroys all data)
make reset-db
```

### Code Quality

The project uses several tools for code quality:

- **Ruff** - Fast Python linter
- **Black** - Code formatter
- **isort** - Import sorter
- **MyPy** - Type checker
- **Pytest** - Testing framework

Run all checks:
```bash
make lint
make typecheck
make test
```

## API Documentation

### Core Endpoints

#### Projects
- `GET /api/v1/projects` - List projects
- `POST /api/v1/projects` - Create project
- `GET /api/v1/projects/{id}` - Get project
- `PUT /api/v1/projects/{id}` - Update project
- `DELETE /api/v1/projects/{id}` - Delete project

#### Pages
- `POST /api/v1/projects/{id}/pages` - Upload page
- `GET /api/v1/projects/{id}/pages` - List pages
- `GET /api/v1/projects/{id}/pages/{page_id}` - Get page

#### OCR
- `POST /api/v1/ocr/process` - Process OCR
- `GET /api/v1/ocr/jobs/{id}` - Get OCR job
- `GET /api/v1/ocr/statistics` - OCR statistics

#### Translation
- `POST /api/v1/translation/process` - Process translation
- `GET /api/v1/translation/jobs/{id}` - Get translation job
- `GET /api/v1/translation/templates` - List templates

#### LLM Providers
- `GET /api/v1/llm/providers` - List available providers
- `POST /api/v1/llm/generate/text` - Generate text
- `POST /api/v1/llm/ocr` - Perform OCR
- `POST /api/v1/llm/translate` - Translate text

### Interactive Documentation

Visit `http://localhost:8000/docs` for Swagger UI documentation or `http://localhost:8000/redoc` for ReDoc.

## Testing

Run the test suite:

```bash
make test
```

Tests are organized by module and include:
- Unit tests for services and utilities
- Integration tests for API endpoints
- Database tests with fixtures

## Deployment

### Local Development

Use the provided Makefile commands for local development.

### Production Considerations

- Use environment variables for configuration
- Set up proper logging and monitoring
- Consider using a process manager like systemd or supervisor
- Implement proper backup strategies for the SQLite database

## Contributing

1. Follow the existing code style and patterns
2. Add tests for new functionality
3. Update documentation as needed
4. Run code quality checks before committing

## License

This project is licensed under the MIT License.
