# Service Layer Architecture

The ho-trans backend implements a service layer pattern with standardized ORM query patterns for consistent, maintainable data access.

## Overview

The service layer provides business logic and data access patterns that:

- **Standardize ORM usage** across all modules
- **Maintain API contracts** while allowing internal optimization
- **Provide consistent error handling** and validation
- **Support efficient aggregation** and complex queries

## ORM Query Patterns

### Single Entity Retrieval

Use `scalar_one_or_none()` for retrieving single model instances:

```python
async def get_project(self, project_id: str) -> ProjectResponse:
    """Get a project by ID."""
    query = select(Project).where(Project.id == project_id)
    result = await self.session.execute(query)
    project = result.scalar_one_or_none()
    
    if not project:
        raise ProjectNotFound(f"Project with ID {project_id} not found")
    
    # Use model's to_dict() method for serialization
    return ProjectResponse(**project.to_dict())
```

**Key Points:**
- Always use `scalar_one_or_none()` for single entity queries
- Never use `first()` with model instance queries
- Use model's `to_dict()` method for response serialization
- Handle null cases explicitly before serialization

### Multiple Entity Retrieval

Use `scalars().all()` for retrieving multiple model instances:

```python
async def get_page_text_regions(self, page_id: str) -> List[TextRegionResponse]:
    """Get all text regions for a page."""
    query = select(TextRegion).where(TextRegion.page_id == page_id).order_by(TextRegion.created_at)
    result = await self.session.execute(query)
    text_regions = result.scalars().all()
    
    return [TextRegionResponse(**text_region.to_dict()) for text_region in text_regions]
```

**Key Points:**
- Always use `scalars().all()` for multiple entity queries
- Never use `fetchall()` with model instance queries
- Iterate over actual model instances, not row mappings
- Apply consistent ordering for predictable results

### Aggregated Data Patterns

Use separate efficient queries for aggregated fields:

```python
async def get_project(self, project_id: str) -> ProjectResponse:
    """Get a project by ID with page count."""
    # Get the project
    query = select(Project).where(Project.id == project_id)
    result = await self.session.execute(query)
    project = result.scalar_one_or_none()
    
    if not project:
        raise ProjectNotFound(f"Project with ID {project_id} not found")

    # Get page count with separate query
    page_count_query = select(func.count(ProjectPage.id)).where(ProjectPage.project_id == project_id)
    page_count_result = await self.session.execute(page_count_query)
    page_count = page_count_result.scalar() or 0

    # Combine data
    project_dict = project.to_dict()
    project_dict["page_count"] = page_count
    return ProjectResponse(**project_dict)
```

**Batch Aggregation for Multiple Entities:**

```python
async def get_projects(self, limit: int = 10, offset: int = 0) -> List[ProjectResponse]:
    """Get projects with page counts."""
    # Get projects
    query = select(Project).order_by(Project.updated_at.desc()).limit(limit).offset(offset)
    result = await self.session.execute(query)
    projects = result.scalars().all()

    # Get page counts for all projects in one query
    project_ids = [project.id for project in projects]
    if project_ids:
        page_count_query = select(
            ProjectPage.project_id,
            func.count(ProjectPage.id).label("page_count")
        ).where(ProjectPage.project_id.in_(project_ids)).group_by(ProjectPage.project_id)
        page_count_result = await self.session.execute(page_count_query)
        page_counts = {row.project_id: row.page_count for row in page_count_result}
    else:
        page_counts = {}

    # Build responses
    responses = []
    for project in projects:
        project_dict = project.to_dict()
        project_dict["page_count"] = page_counts.get(project.id, 0)
        responses.append(ProjectResponse(**project_dict))

    return responses
```

**Key Points:**
- Use separate queries for aggregated fields instead of complex joins
- Batch aggregation queries when processing multiple entities
- Never sacrifice query efficiency for code brevity
- Consider performance implications of N+1 query patterns

## Pagination with ORM

### Standard Pagination

Use the `paginate_orm()` method for ORM model instances:

```python
async def get_projects_paginated(self, params: PaginationParams) -> PaginatedResponse[ProjectResponse]:
    """Get projects with pagination using ORM approach."""
    # Get projects with pagination
    query = select(Project).order_by(Project.updated_at.desc())
    projects, total = await self.paginator.paginate_orm(query, params)

    # Convert to response objects
    project_responses = [ProjectResponse(**project.to_dict()) for project in projects]
    return PaginatedResponse.create(project_responses, total, params)
```

### Pagination with Aggregation

Combine pagination with efficient aggregation:

```python
async def get_projects_paginated(self, params: PaginationParams) -> PaginatedResponse[ProjectResponse]:
    """Get projects with pagination and page counts."""
    # Get projects with pagination
    query = select(Project).order_by(Project.updated_at.desc())
    projects, total = await self.paginator.paginate_orm(query, params)

    # Get page counts for paginated projects
    project_ids = [project.id for project in projects]
    if project_ids:
        page_count_query = select(
            ProjectPage.project_id,
            func.count(ProjectPage.id).label("page_count")
        ).where(ProjectPage.project_id.in_(project_ids)).group_by(ProjectPage.project_id)
        page_count_result = await self.session.execute(page_count_query)
        page_counts = {row.project_id: row.page_count for row in page_count_result}
    else:
        page_counts = {}

    # Build responses with aggregated data
    project_responses = []
    for project in projects:
        project_dict = project.to_dict()
        project_dict["page_count"] = page_counts.get(project.id, 0)
        project_responses.append(ProjectResponse(**project_dict))

    return PaginatedResponse.create(project_responses, total, params)
```

## Service Class Structure

### Standard Service Pattern

```python
class ProjectService:
    """Service class for project management operations."""

    def __init__(self, session: AsyncSession):
        self.session = session
        self.paginator = Paginator(session)

    async def create_project(self, project_data: ProjectCreate) -> ProjectResponse:
        """Create a new project."""
        # Create ORM model instance
        project = Project(
            name=project_data.name,
            description=project_data.description,
            source_language=project_data.source_language,
            target_language=project_data.target_language,
            status=ProjectStatus.DRAFT
        )

        self.session.add(project)
        await self.session.commit()
        await self.session.refresh(project)

        return await self.get_project(project.id)

    async def get_project(self, project_id: str) -> ProjectResponse:
        """Get a project by ID."""
        # Implementation using ORM patterns...
```

### Key Principles

1. **Dependency Injection**: Accept AsyncSession in constructor
2. **ORM Model Usage**: Always work with model instances
3. **Consistent Error Handling**: Use domain-specific exceptions
4. **Transaction Management**: Use session.commit() and session.refresh()
5. **Response Consistency**: Always return response schemas

## Error Handling

### Domain-Specific Exceptions

```python
# Define in exceptions.py
class ProjectNotFound(HTTPException):
    def __init__(self, message: str):
        super().__init__(status_code=404, detail=message)

# Use in service methods
async def get_project(self, project_id: str) -> ProjectResponse:
    project = result.scalar_one_or_none()
    if not project:
        raise ProjectNotFound(f"Project with ID {project_id} not found")
```

### Validation and Constraints

```python
async def update_project(self, project_id: str, project_data: ProjectUpdate) -> ProjectResponse:
    """Update a project."""
    # Check if project exists first
    await self.get_project(project_id)

    # Build update data, excluding unset fields
    update_data = project_data.model_dump(exclude_unset=True)
    if not update_data:
        return await self.get_project(project_id)

    # Apply updates
    query = update(Project).where(Project.id == project_id).values(**update_data)
    await self.session.execute(query)
    await self.session.commit()

    return await self.get_project(project_id)
```

## Testing Service Methods

### Unit Test Structure

```python
import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from src.projects.service import ProjectService
from src.projects.schemas import ProjectCreate

@pytest.mark.asyncio
async def test_create_project(db_session: AsyncSession):
    """Test project creation."""
    service = ProjectService(db_session)
    
    project_data = ProjectCreate(
        name="Test Project",
        description="Test Description",
        source_language="japanese",
        target_language="english"
    )
    
    result = await service.create_project(project_data)
    
    assert result.name == "Test Project"
    assert result.status == ProjectStatus.DRAFT
    assert result.page_count == 0
```

### Integration Test Patterns

```python
@pytest.mark.asyncio
async def test_project_with_pages(db_session: AsyncSession):
    """Test project with page count aggregation."""
    service = ProjectService(db_session)
    
    # Create project
    project = await service.create_project(project_data)
    
    # Add pages
    page1 = await service.create_project_page(project.id, page_data1, ...)
    page2 = await service.create_project_page(project.id, page_data2, ...)
    
    # Verify aggregation
    updated_project = await service.get_project(project.id)
    assert updated_project.page_count == 2
```

## Migration Guidelines

### Converting Existing Services

1. **Update single entity queries:**
   ```python
   # Before
   row = result.first()
   return Response(**dict(row._mapping))
   
   # After
   model = result.scalar_one_or_none()
   return Response(**model.to_dict())
   ```

2. **Update multiple entity queries:**
   ```python
   # Before
   rows = result.fetchall()
   return [Response(**dict(row._mapping)) for row in rows]
   
   # After
   models = result.scalars().all()
   return [Response(**model.to_dict()) for model in models]
   ```

3. **Update pagination:**
   ```python
   # Before
   items, total = await self.paginator.paginate(query, params)
   
   # After
   models, total = await self.paginator.paginate_orm(query, params)
   ```

## Best Practices

1. **Always use ORM model instances** for consistency
2. **Separate aggregation queries** for better performance
3. **Batch aggregation** when processing multiple entities
4. **Handle null cases explicitly** before serialization
5. **Use domain-specific exceptions** for clear error handling
6. **Test both positive and negative scenarios**
7. **Maintain API contracts** during internal refactoring
8. **Document complex query patterns** for future maintenance
