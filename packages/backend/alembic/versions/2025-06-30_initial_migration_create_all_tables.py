"""Initial migration: create all tables

Revision ID: 30aad5358760
Revises: 
Create Date: 2025-06-30 11:23:10.373008

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '30aad5358760'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('project',
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('source_language', sa.String(length=50), nullable=False),
    sa.Column('target_language', sa.String(length=50), nullable=False),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id', name=op.f('project_pkey'))
    )
    op.create_index(op.f('project_name_idx'), 'project', ['name'], unique=False)
    op.create_table('translation_template',
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('source_language', sa.String(length=50), nullable=False),
    sa.Column('target_language', sa.String(length=50), nullable=False),
    sa.Column('source_pattern', sa.Text(), nullable=False),
    sa.Column('target_pattern', sa.Text(), nullable=False),
    sa.Column('usage_count', sa.Integer(), nullable=False),
    sa.Column('category', sa.String(length=100), nullable=True),
    sa.Column('tags', sa.JSON(), nullable=True),
    sa.Column('average_quality_score', sa.Float(), nullable=True),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id', name=op.f('translation_template_pkey'))
    )
    op.create_table('project_page',
    sa.Column('project_id', sa.String(length=36), nullable=False),
    sa.Column('page_number', sa.Integer(), nullable=False),
    sa.Column('original_filename', sa.String(length=255), nullable=False),
    sa.Column('file_path', sa.String(length=500), nullable=False),
    sa.Column('file_size', sa.Integer(), nullable=False),
    sa.Column('image_width', sa.Integer(), nullable=True),
    sa.Column('image_height', sa.Integer(), nullable=True),
    sa.Column('ocr_status', sa.String(length=50), nullable=False),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['project_id'], ['project.id'], name=op.f('project_page_project_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('project_page_pkey'))
    )
    op.create_table('ocr_job',
    sa.Column('page_id', sa.String(length=36), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('provider', sa.String(length=50), nullable=False),
    sa.Column('prompt_used', sa.Text(), nullable=True),
    sa.Column('raw_response', sa.Text(), nullable=True),
    sa.Column('processing_time_seconds', sa.Float(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('retry_count', sa.Integer(), nullable=False),
    sa.Column('total_regions_detected', sa.Integer(), nullable=False),
    sa.Column('average_confidence', sa.Float(), nullable=True),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['page_id'], ['project_page.id'], name=op.f('ocr_job_page_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('ocr_job_pkey'))
    )
    op.create_table('text_region',
    sa.Column('page_id', sa.String(length=36), nullable=False),
    sa.Column('region_type', sa.String(length=50), nullable=False),
    sa.Column('x', sa.Float(), nullable=False),
    sa.Column('y', sa.Float(), nullable=False),
    sa.Column('width', sa.Float(), nullable=False),
    sa.Column('height', sa.Float(), nullable=False),
    sa.Column('original_text', sa.Text(), nullable=True),
    sa.Column('confidence_score', sa.Float(), nullable=True),
    sa.Column('translated_text', sa.Text(), nullable=True),
    sa.Column('translation_status', sa.String(length=50), nullable=False),
    sa.Column('font_family', sa.String(length=100), nullable=True),
    sa.Column('font_size', sa.Integer(), nullable=True),
    sa.Column('font_color', sa.String(length=7), nullable=True),
    sa.Column('background_color', sa.String(length=7), nullable=True),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['page_id'], ['project_page.id'], name=op.f('text_region_page_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('text_region_pkey'))
    )
    op.create_table('ocr_result',
    sa.Column('job_id', sa.String(length=36), nullable=False),
    sa.Column('detected_text', sa.Text(), nullable=False),
    sa.Column('confidence_score', sa.Float(), nullable=True),
    sa.Column('region_type', sa.String(length=50), nullable=True),
    sa.Column('x', sa.Float(), nullable=True),
    sa.Column('y', sa.Float(), nullable=True),
    sa.Column('width', sa.Float(), nullable=True),
    sa.Column('height', sa.Float(), nullable=True),
    sa.Column('extra_data', sa.JSON(), nullable=True),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['job_id'], ['ocr_job.id'], name=op.f('ocr_result_job_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('ocr_result_pkey'))
    )
    op.create_table('translation_job',
    sa.Column('text_region_id', sa.String(length=36), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('provider', sa.String(length=50), nullable=False),
    sa.Column('source_language', sa.String(length=50), nullable=False),
    sa.Column('target_language', sa.String(length=50), nullable=False),
    sa.Column('original_text', sa.Text(), nullable=False),
    sa.Column('translated_text', sa.Text(), nullable=True),
    sa.Column('prompt_used', sa.Text(), nullable=True),
    sa.Column('raw_response', sa.Text(), nullable=True),
    sa.Column('processing_time_seconds', sa.Float(), nullable=True),
    sa.Column('confidence_score', sa.Float(), nullable=True),
    sa.Column('quality_score', sa.Float(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('retry_count', sa.Integer(), nullable=False),
    sa.Column('extra_data', sa.JSON(), nullable=True),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['text_region_id'], ['text_region.id'], name=op.f('translation_job_text_region_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('translation_job_pkey'))
    )
    op.create_table('translation_alternative',
    sa.Column('job_id', sa.String(length=36), nullable=False),
    sa.Column('translated_text', sa.Text(), nullable=False),
    sa.Column('confidence_score', sa.Float(), nullable=True),
    sa.Column('quality_score', sa.Float(), nullable=True),
    sa.Column('rank', sa.Integer(), nullable=False),
    sa.Column('is_selected', sa.String(length=5), nullable=False),
    sa.Column('extra_data', sa.JSON(), nullable=True),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['job_id'], ['translation_job.id'], name=op.f('translation_alternative_job_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('translation_alternative_pkey'))
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('translation_alternative')
    op.drop_table('translation_job')
    op.drop_table('ocr_result')
    op.drop_table('text_region')
    op.drop_table('ocr_job')
    op.drop_table('project_page')
    op.drop_table('translation_template')
    op.drop_index(op.f('project_name_idx'), table_name='project')
    op.drop_table('project')
    # ### end Alembic commands ###
