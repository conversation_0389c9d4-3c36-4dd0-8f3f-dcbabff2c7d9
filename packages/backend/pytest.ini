[tool:pytest]
# Pytest configuration for ho-trans backend

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Async configuration
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function

# Output configuration
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=src
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=80

# Markers
markers =
    asyncio: marks tests as async
    unit: marks tests as unit tests
    integration: marks tests as integration tests
    slow: marks tests as slow running
    external: marks tests that require external services

# Warnings
filterwarnings =
    error
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*unclosed.*:ResourceWarning

# Minimum version
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Parallel execution
# Uncomment to enable parallel test execution
# addopts = -n auto
