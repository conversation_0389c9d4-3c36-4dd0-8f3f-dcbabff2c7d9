"""
Test utilities and helpers for ho-trans backend tests.
"""
import asyncio
from typing import Any, Dict, List, Optional
from unittest.mock import AsyncMock, MagicMock
from uuid import uuid4

from databases import Database
from sqlalchemy import create_engine, MetaData
from sqlalchemy.pool import StaticPool

from src.database import Base
from src.pagination import <PERSON>ginationParams, PaginatedResponse


class MockDatabase:
    """Mock database for testing."""
    
    def __init__(self):
        self.data: Dict[str, List[Dict[str, Any]]] = {}
        self.connected = False
    
    async def connect(self):
        """Mock connect method."""
        self.connected = True
    
    async def disconnect(self):
        """Mock disconnect method."""
        self.connected = False
    
    async def fetch_one(self, query) -> Optional[Dict[str, Any]]:
        """Mock fetch_one method."""
        # Simple mock implementation
        return {"id": "mock-id", "name": "mock-item"}
    
    async def fetch_all(self, query) -> List[Dict[str, Any]]:
        """Mock fetch_all method."""
        # Simple mock implementation
        return [
            {"id": "mock-id-1", "name": "mock-item-1"},
            {"id": "mock-id-2", "name": "mock-item-2"}
        ]
    
    async def fetch_val(self, query) -> Any:
        """Mock fetch_val method."""
        # Simple mock implementation for count queries
        return 10
    
    async def execute(self, query) -> Any:
        """Mock execute method."""
        return None


def create_test_database() -> Database:
    """Create an in-memory test database."""
    database_url = "sqlite:///:memory:"
    return Database(database_url)


def create_test_engine():
    """Create a test SQLAlchemy engine."""
    return create_engine(
        "sqlite:///:memory:",
        connect_args={
            "check_same_thread": False,
        },
        poolclass=StaticPool,
    )


async def setup_test_database() -> Database:
    """Set up a test database with tables."""
    database = create_test_database()
    engine = create_test_engine()
    
    # Create tables
    Base.metadata.create_all(engine)
    
    # Connect to database
    await database.connect()
    
    return database


def create_mock_pagination_response(
    items: List[Any],
    page: int = 1,
    limit: int = 10,
    total: int = None
) -> PaginatedResponse:
    """Create a mock paginated response."""
    if total is None:
        total = len(items)
    
    params = PaginationParams(page=page, limit=limit)
    return PaginatedResponse.create(items, total, params)


def generate_uuid() -> str:
    """Generate a test UUID."""
    return str(uuid4())


class AsyncContextManager:
    """Helper for async context managers in tests."""
    
    def __init__(self, return_value=None):
        self.return_value = return_value
    
    async def __aenter__(self):
        return self.return_value
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass


def create_mock_llm_response(
    content: str = "Mock response",
    provider: str = "claude",
    model: str = "claude-3-5-sonnet-20241022"
) -> AsyncMock:
    """Create a mock LLM response."""
    mock_response = AsyncMock()
    mock_response.content = content
    mock_response.provider = provider
    mock_response.model = model
    mock_response.usage = {
        "prompt_tokens": 10,
        "completion_tokens": 5,
        "total_tokens": 15
    }
    mock_response.metadata = {"stop_reason": "end_turn"}
    return mock_response


def create_mock_project_data(
    project_id: str = None,
    name: str = "Test Project",
    status: str = "draft"
) -> Dict[str, Any]:
    """Create mock project data."""
    return {
        "id": project_id or generate_uuid(),
        "name": name,
        "description": "A test project",
        "status": status,
        "source_language": "japanese",
        "target_language": "english",
        "page_count": 0,
        "created_at": "2025-06-30T10:00:00Z",
        "updated_at": "2025-06-30T10:00:00Z"
    }


def create_mock_page_data(
    page_id: str = None,
    project_id: str = None,
    page_number: int = 1
) -> Dict[str, Any]:
    """Create mock page data."""
    return {
        "id": page_id or generate_uuid(),
        "project_id": project_id or generate_uuid(),
        "page_number": page_number,
        "original_filename": f"page_{page_number:03d}.jpg",
        "file_path": f"/uploads/page_{page_number:03d}.jpg",
        "file_size": 1024000,
        "image_width": 800,
        "image_height": 1200,
        "ocr_status": "pending",
        "text_region_count": 0,
        "created_at": "2025-06-30T10:00:00Z",
        "updated_at": "2025-06-30T10:00:00Z"
    }


def create_mock_text_region_data(
    region_id: str = None,
    page_id: str = None,
    text: str = "こんにちは"
) -> Dict[str, Any]:
    """Create mock text region data."""
    return {
        "id": region_id or generate_uuid(),
        "page_id": page_id or generate_uuid(),
        "region_type": "speech_bubble",
        "x": 0.1,
        "y": 0.2,
        "width": 0.3,
        "height": 0.1,
        "original_text": text,
        "confidence_score": 0.95,
        "translated_text": None,
        "translation_status": "pending",
        "font_family": None,
        "font_size": None,
        "font_color": None,
        "background_color": None,
        "created_at": "2025-06-30T10:00:00Z",
        "updated_at": "2025-06-30T10:00:00Z"
    }


def create_mock_ocr_job_data(
    job_id: str = None,
    page_id: str = None,
    status: str = "pending"
) -> Dict[str, Any]:
    """Create mock OCR job data."""
    return {
        "id": job_id or generate_uuid(),
        "page_id": page_id or generate_uuid(),
        "status": status,
        "provider": "claude",
        "prompt_used": "Extract text from this image",
        "raw_response": None,
        "processing_time_seconds": None,
        "error_message": None,
        "retry_count": 0,
        "total_regions_detected": 0,
        "average_confidence": None,
        "created_at": "2025-06-30T10:00:00Z",
        "updated_at": "2025-06-30T10:00:00Z"
    }


def create_mock_translation_job_data(
    job_id: str = None,
    text_region_id: str = None,
    status: str = "pending"
) -> Dict[str, Any]:
    """Create mock translation job data."""
    return {
        "id": job_id or generate_uuid(),
        "text_region_id": text_region_id or generate_uuid(),
        "status": status,
        "provider": "claude",
        "source_language": "japanese",
        "target_language": "english",
        "original_text": "こんにちは",
        "translated_text": None,
        "prompt_used": "Translate this text",
        "raw_response": None,
        "processing_time_seconds": None,
        "confidence_score": None,
        "quality_score": None,
        "error_message": None,
        "retry_count": 0,
        "metadata": {},
        "created_at": "2025-06-30T10:00:00Z",
        "updated_at": "2025-06-30T10:00:00Z"
    }


class TestEventLoop:
    """Test event loop utilities to prevent event loop issues."""
    
    @staticmethod
    def get_or_create_event_loop():
        """Get or create an event loop for testing."""
        try:
            loop = asyncio.get_running_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        return loop
    
    @staticmethod
    def run_async_test(coro):
        """Run an async test function."""
        loop = TestEventLoop.get_or_create_event_loop()
        return loop.run_until_complete(coro)


def assert_pagination_response(
    response_data: Dict[str, Any],
    expected_items_count: int,
    expected_page: int = 1,
    expected_limit: int = 10
):
    """Assert that a response has proper pagination structure."""
    assert "items" in response_data
    assert "meta" in response_data
    
    # Check items
    assert isinstance(response_data["items"], list)
    assert len(response_data["items"]) == expected_items_count
    
    # Check meta
    meta = response_data["meta"]
    assert meta["page"] == expected_page
    assert meta["limit"] == expected_limit
    assert isinstance(meta["total"], int)
    assert isinstance(meta["pages"], int)
    assert isinstance(meta["has_prev"], bool)
    assert isinstance(meta["has_next"], bool)


def assert_error_response(
    response_data: Dict[str, Any],
    expected_status_code: int = None
):
    """Assert that a response is a proper error response."""
    assert "detail" in response_data
    
    if expected_status_code:
        # This would need to be checked at the response level
        pass
