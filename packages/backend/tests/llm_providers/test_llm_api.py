"""
Integration tests for LLM providers API endpoints.
"""
import pytest
from httpx import Async<PERSON>lient
from unittest.mock import AsyncMock, patch
from io import BytesIO


@pytest.mark.asyncio
async def test_get_providers_status(client: AsyncClient):
    """Test getting LLM providers status."""
    with patch('src.llm_providers.factory.LLMProviderFactory.is_provider_available') as mock_available:
        with patch('src.llm_providers.factory.LLMProviderFactory.create_client') as mock_create:
            # Mock provider availability
            mock_available.side_effect = lambda provider: provider.value == "claude"
            
            # Mock client creation
            mock_client = AsyncMock()
            mock_client.get_default_model.return_value = "claude-3-5-sonnet-20241022"
            mock_client.get_available_models.return_value = ["claude-3-5-sonnet-20241022", "claude-3-haiku-20240307"]
            mock_create.return_value = mock_client
            
            response = await client.get("/api/v1/llm/providers")
            
            assert response.status_code == 200
            data = response.json()
            assert "providers" in data
            assert "total_available" in data
            assert "default_provider" in data
            
            # Check that at least one provider is listed
            assert len(data["providers"]) >= 1


@pytest.mark.asyncio
async def test_get_provider_models(client: AsyncClient):
    """Test getting available models for a provider."""
    provider = "claude"
    
    with patch('src.llm_providers.factory.LLMProviderFactory.is_provider_available') as mock_available:
        with patch('src.llm_providers.factory.LLMProviderFactory.create_client') as mock_create:
            mock_available.return_value = True
            
            mock_client = AsyncMock()
            mock_client.get_available_models.return_value = [
                "claude-3-5-sonnet-20241022",
                "claude-3-haiku-20240307"
            ]
            mock_create.return_value = mock_client
            
            response = await client.get(f"/api/v1/llm/providers/{provider}/models")
            
            assert response.status_code == 200
            data = response.json()
            assert isinstance(data, list)
            assert len(data) >= 1


@pytest.mark.asyncio
async def test_generate_text(client: AsyncClient):
    """Test text generation endpoint."""
    request_data = {
        "prompt": "Translate 'こんにちは' to English",
        "provider": "claude",
        "model": "claude-3-5-sonnet-20241022",
        "temperature": 0.7,
        "max_tokens": 100
    }
    
    with patch('src.llm_providers.service.llm_service.generate_text') as mock_generate:
        mock_response = AsyncMock()
        mock_response.content = "Hello"
        mock_response.provider = "claude"
        mock_response.model = "claude-3-5-sonnet-20241022"
        mock_response.usage = {"prompt_tokens": 10, "completion_tokens": 5, "total_tokens": 15}
        mock_response.metadata = {"stop_reason": "end_turn"}
        mock_generate.return_value = mock_response
        
        response = await client.post("/api/v1/llm/generate/text", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["content"] == "Hello"
        assert data["provider"] == "claude"
        assert "processing_time_ms" in data


@pytest.mark.asyncio
async def test_generate_text_batch(client: AsyncClient):
    """Test batch text generation endpoint."""
    request_data = {
        "prompts": [
            "Translate 'こんにちは' to English",
            "Translate 'ありがとう' to English"
        ],
        "provider": "claude",
        "temperature": 0.7
    }
    
    with patch('src.llm_providers.service.llm_service.generate_text') as mock_generate:
        # Mock responses for each prompt
        mock_responses = []
        for i, prompt in enumerate(request_data["prompts"]):
            mock_response = AsyncMock()
            mock_response.content = f"Response {i+1}"
            mock_response.provider = "claude"
            mock_response.model = "claude-3-5-sonnet-20241022"
            mock_response.usage = {"prompt_tokens": 10, "completion_tokens": 5, "total_tokens": 15}
            mock_response.metadata = {}
            mock_responses.append(mock_response)
        
        mock_generate.side_effect = mock_responses
        
        response = await client.post("/api/v1/llm/generate/batch", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "results" in data
        assert len(data["results"]) == 2
        assert data["total_requests"] == 2
        assert data["successful_requests"] == 2


@pytest.mark.asyncio
async def test_analyze_image(client: AsyncClient):
    """Test image analysis endpoint."""
    # Create a mock image file
    image_data = b"fake_image_data"
    files = {"file": ("test_image.jpg", BytesIO(image_data), "image/jpeg")}
    form_data = {
        "prompt": "Describe this image",
        "provider": "claude",
        "temperature": "0.7"
    }
    
    with patch('src.llm_providers.service.llm_service.analyze_image') as mock_analyze:
        mock_response = AsyncMock()
        mock_response.content = "This is a manga page with speech bubbles"
        mock_response.provider = "claude"
        mock_response.model = "claude-3-5-sonnet-20241022"
        mock_response.usage = {"prompt_tokens": 20, "completion_tokens": 10, "total_tokens": 30}
        mock_response.metadata = {"image_format": "jpeg"}
        mock_analyze.return_value = mock_response
        
        response = await client.post("/api/v1/llm/analyze/image", files=files, data=form_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["content"] == "This is a manga page with speech bubbles"
        assert data["provider"] == "claude"


@pytest.mark.asyncio
async def test_perform_ocr(client: AsyncClient):
    """Test OCR endpoint."""
    # Create a mock image file
    image_data = b"fake_image_data"
    files = {"file": ("manga_page.jpg", BytesIO(image_data), "image/jpeg")}
    form_data = {
        "provider": "claude",
        "custom_prompt": "Extract all text from this manga page"
    }
    
    with patch('src.llm_providers.service.llm_service.perform_ocr') as mock_ocr:
        mock_response = {
            "regions": [
                {
                    "text": "こんにちは",
                    "type": "speech_bubble",
                    "confidence": 0.95,
                    "coordinates": {
                        "x": 0.1,
                        "y": 0.2,
                        "width": 0.3,
                        "height": 0.1
                    }
                }
            ],
            "metadata": {
                "processing_time": 2.5,
                "model_used": "claude-3-5-sonnet-20241022"
            }
        }
        mock_ocr.return_value = mock_response
        
        response = await client.post("/api/v1/llm/ocr", files=files, data=form_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "regions" in data
        assert len(data["regions"]) == 1
        assert data["regions"][0]["text"] == "こんにちは"


@pytest.mark.asyncio
async def test_translate_text(client: AsyncClient):
    """Test translation endpoint."""
    request_data = {
        "text": "こんにちは",
        "source_language": "japanese",
        "target_language": "english",
        "text_type": "dialogue",
        "provider": "claude",
        "generate_alternatives": True
    }
    
    with patch('src.llm_providers.service.llm_service.perform_translation') as mock_translate:
        mock_response = {
            "translated_text": "Hello",
            "confidence": 0.95,
            "alternatives": ["Hi", "Greetings"],
            "metadata": {
                "model_used": "claude-3-5-sonnet-20241022",
                "processing_time": 1.2
            }
        }
        mock_translate.return_value = mock_response
        
        response = await client.post("/api/v1/llm/translate", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["translated_text"] == "Hello"
        assert data["confidence"] == 0.95
        assert "alternatives" in data


@pytest.mark.asyncio
async def test_image_validation_error(client: AsyncClient):
    """Test image upload validation."""
    # Test with non-image file
    text_data = b"This is not an image"
    files = {"file": ("test.txt", BytesIO(text_data), "text/plain")}
    
    response = await client.post("/api/v1/llm/analyze/image", files=files)
    
    assert response.status_code == 400
    data = response.json()
    assert "File must be an image" in data["detail"]


@pytest.mark.asyncio
async def test_provider_not_available(client: AsyncClient):
    """Test handling of unavailable provider."""
    provider = "unavailable_provider"
    
    with patch('src.llm_providers.factory.LLMProviderFactory.is_provider_available') as mock_available:
        mock_available.return_value = False
        
        response = await client.get(f"/api/v1/llm/providers/{provider}/models")
        
        assert response.status_code == 400
        data = response.json()
        assert "not available" in data["detail"]


@pytest.mark.asyncio
async def test_text_generation_error(client: AsyncClient):
    """Test handling of text generation errors."""
    request_data = {
        "prompt": "Test prompt",
        "provider": "claude"
    }
    
    with patch('src.llm_providers.service.llm_service.generate_text') as mock_generate:
        mock_generate.side_effect = Exception("API error")
        
        response = await client.post("/api/v1/llm/generate/text", json=request_data)
        
        assert response.status_code == 500
        data = response.json()
        assert "Text generation failed" in data["detail"]


@pytest.mark.asyncio
async def test_batch_generation_partial_failure(client: AsyncClient):
    """Test batch generation with some failures."""
    request_data = {
        "prompts": ["Good prompt", "Bad prompt"],
        "provider": "claude"
    }
    
    with patch('src.llm_providers.service.llm_service.generate_text') as mock_generate:
        # First call succeeds, second fails
        mock_success = AsyncMock()
        mock_success.content = "Success response"
        mock_success.provider = "claude"
        mock_success.model = "claude-3-5-sonnet-20241022"
        mock_success.usage = {}
        mock_success.metadata = {}
        
        mock_generate.side_effect = [mock_success, Exception("API error")]
        
        response = await client.post("/api/v1/llm/generate/batch", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["total_requests"] == 2
        assert data["successful_requests"] == 1
        assert data["failed_requests"] == 1
