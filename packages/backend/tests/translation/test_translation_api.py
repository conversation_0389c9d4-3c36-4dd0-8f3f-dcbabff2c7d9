"""
Integration tests for translation API endpoints.
"""
import pytest
from httpx import AsyncClient
from unittest.mock import AsyncMock, patch


@pytest.mark.asyncio
async def test_create_translation_job(client: AsyncClient, sample_translation_job_data):
    """Test creating a new translation job."""
    with patch('src.translation.service.TranslationService.create_translation_job') as mock_create:
        mock_response = {
            "id": "test-translation-job-id",
            "text_region_id": sample_translation_job_data["text_region_id"],
            "status": "pending",
            "provider": sample_translation_job_data["provider"],
            "source_language": sample_translation_job_data["source_language"],
            "target_language": sample_translation_job_data["target_language"],
            "original_text": sample_translation_job_data["original_text"],
            "translated_text": None,
            "prompt_used": "Translate this text...",
            "processing_time_seconds": None,
            "confidence_score": None,
            "quality_score": None,
            "error_message": None,
            "retry_count": 0,
            "metadata": {},
            "created_at": "2025-06-30T10:00:00Z",
            "updated_at": "2025-06-30T10:00:00Z"
        }
        mock_create.return_value = mock_response
        
        response = await client.post("/api/v1/translation/jobs", json=sample_translation_job_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["text_region_id"] == sample_translation_job_data["text_region_id"]
        assert data["status"] == "pending"
        assert data["original_text"] == sample_translation_job_data["original_text"]


@pytest.mark.asyncio
async def test_get_translation_job(client: AsyncClient):
    """Test getting a translation job by ID."""
    job_id = "test-translation-job-id"
    
    with patch('src.translation.service.TranslationService.get_translation_job') as mock_get:
        mock_job = {
            "id": job_id,
            "text_region_id": "test-region-id",
            "status": "completed",
            "provider": "claude",
            "source_language": "japanese",
            "target_language": "english",
            "original_text": "こんにちは",
            "translated_text": "Hello",
            "prompt_used": "Translate this text...",
            "processing_time_seconds": 1.2,
            "confidence_score": 0.95,
            "quality_score": 0.92,
            "error_message": None,
            "retry_count": 0,
            "metadata": {},
            "created_at": "2025-06-30T10:00:00Z",
            "updated_at": "2025-06-30T10:02:00Z"
        }
        mock_get.return_value = mock_job
        
        response = await client.get(f"/api/v1/translation/jobs/{job_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == job_id
        assert data["status"] == "completed"
        assert data["translated_text"] == "Hello"


@pytest.mark.asyncio
async def test_get_translation_job_detail(client: AsyncClient):
    """Test getting detailed translation job with alternatives."""
    job_id = "test-translation-job-id"
    
    with patch('src.translation.service.TranslationService.get_translation_job_detail') as mock_get_detail:
        mock_detail = {
            "id": job_id,
            "text_region_id": "test-region-id",
            "status": "completed",
            "provider": "claude",
            "source_language": "japanese",
            "target_language": "english",
            "original_text": "こんにちは",
            "translated_text": "Hello",
            "prompt_used": "Translate this text...",
            "processing_time_seconds": 1.2,
            "confidence_score": 0.95,
            "quality_score": 0.92,
            "error_message": None,
            "retry_count": 0,
            "metadata": {},
            "created_at": "2025-06-30T10:00:00Z",
            "updated_at": "2025-06-30T10:02:00Z",
            "alternatives": [
                {
                    "id": "alt-1",
                    "job_id": job_id,
                    "translated_text": "Hello",
                    "confidence_score": 0.95,
                    "quality_score": 0.92,
                    "rank": 1,
                    "is_selected": True,
                    "metadata": {},
                    "created_at": "2025-06-30T10:02:00Z",
                    "updated_at": "2025-06-30T10:02:00Z"
                },
                {
                    "id": "alt-2",
                    "job_id": job_id,
                    "translated_text": "Hi",
                    "confidence_score": 0.88,
                    "quality_score": 0.85,
                    "rank": 2,
                    "is_selected": False,
                    "metadata": {},
                    "created_at": "2025-06-30T10:02:00Z",
                    "updated_at": "2025-06-30T10:02:00Z"
                }
            ]
        }
        mock_get_detail.return_value = mock_detail
        
        response = await client.get(f"/api/v1/translation/jobs/{job_id}/detail")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == job_id
        assert "alternatives" in data
        assert len(data["alternatives"]) == 2
        assert data["alternatives"][0]["is_selected"] is True


@pytest.mark.asyncio
async def test_get_translation_alternatives(client: AsyncClient):
    """Test getting translation alternatives for a job."""
    job_id = "test-translation-job-id"
    
    with patch('src.translation.service.TranslationService.get_translation_alternatives') as mock_get_alts:
        mock_alternatives = [
            {
                "id": "alt-1",
                "job_id": job_id,
                "translated_text": "Hello",
                "confidence_score": 0.95,
                "quality_score": 0.92,
                "rank": 1,
                "is_selected": True,
                "metadata": {},
                "created_at": "2025-06-30T10:02:00Z",
                "updated_at": "2025-06-30T10:02:00Z"
            },
            {
                "id": "alt-2",
                "job_id": job_id,
                "translated_text": "Hi",
                "confidence_score": 0.88,
                "quality_score": 0.85,
                "rank": 2,
                "is_selected": False,
                "metadata": {},
                "created_at": "2025-06-30T10:02:00Z",
                "updated_at": "2025-06-30T10:02:00Z"
            }
        ]
        mock_get_alts.return_value = mock_alternatives
        
        response = await client.get(f"/api/v1/translation/jobs/{job_id}/alternatives")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert data[0]["translated_text"] == "Hello"
        assert data[1]["translated_text"] == "Hi"


@pytest.mark.asyncio
async def test_select_translation_alternative(client: AsyncClient):
    """Test selecting a translation alternative."""
    job_id = "test-translation-job-id"
    alternative_id = "alt-2"
    
    with patch('src.translation.service.TranslationService.select_translation_alternative') as mock_select:
        mock_updated_job = {
            "id": job_id,
            "text_region_id": "test-region-id",
            "status": "completed",
            "provider": "claude",
            "source_language": "japanese",
            "target_language": "english",
            "original_text": "こんにちは",
            "translated_text": "Hi",  # Updated to selected alternative
            "prompt_used": "Translate this text...",
            "processing_time_seconds": 1.2,
            "confidence_score": 0.88,
            "quality_score": 0.85,
            "error_message": None,
            "retry_count": 0,
            "metadata": {},
            "created_at": "2025-06-30T10:00:00Z",
            "updated_at": "2025-06-30T10:05:00Z"
        }
        mock_select.return_value = mock_updated_job
        
        response = await client.post(f"/api/v1/translation/jobs/{job_id}/alternatives/{alternative_id}/select")
        
        assert response.status_code == 200
        data = response.json()
        assert data["translated_text"] == "Hi"


@pytest.mark.asyncio
async def test_process_translation(client: AsyncClient):
    """Test processing translation request."""
    request_data = {
        "text_region_id": "test-region-id",
        "provider": "claude",
        "source_language": "japanese",
        "target_language": "english",
        "custom_prompt": "Translate this manga dialogue",
        "generate_alternatives": True,
        "max_alternatives": 3
    }
    
    with patch('src.translation.service.TranslationService.process_translation_request') as mock_process:
        mock_response = {
            "id": "new-translation-job-id",
            "text_region_id": request_data["text_region_id"],
            "status": "in_progress",
            "provider": request_data["provider"],
            "source_language": request_data["source_language"],
            "target_language": request_data["target_language"],
            "original_text": "こんにちは",
            "translated_text": None,
            "prompt_used": request_data["custom_prompt"],
            "processing_time_seconds": None,
            "confidence_score": None,
            "quality_score": None,
            "error_message": None,
            "retry_count": 0,
            "metadata": {},
            "created_at": "2025-06-30T10:00:00Z",
            "updated_at": "2025-06-30T10:00:00Z"
        }
        mock_process.return_value = mock_response
        
        response = await client.post("/api/v1/translation/process", json=request_data)
        
        assert response.status_code == 202  # Accepted
        data = response.json()
        assert data["text_region_id"] == request_data["text_region_id"]
        assert data["status"] == "in_progress"


@pytest.mark.asyncio
async def test_create_translation_template(client: AsyncClient, sample_translation_template_data):
    """Test creating a translation template."""
    with patch('src.translation.service.TranslationService.create_translation_template') as mock_create:
        mock_response = {
            "id": "test-template-id",
            "name": sample_translation_template_data["name"],
            "description": sample_translation_template_data["description"],
            "source_language": sample_translation_template_data["source_language"],
            "target_language": sample_translation_template_data["target_language"],
            "source_pattern": sample_translation_template_data["source_pattern"],
            "target_pattern": sample_translation_template_data["target_pattern"],
            "usage_count": 0,
            "category": sample_translation_template_data["category"],
            "tags": [],
            "average_quality_score": None,
            "created_at": "2025-06-30T10:00:00Z",
            "updated_at": "2025-06-30T10:00:00Z"
        }
        mock_create.return_value = mock_response
        
        response = await client.post("/api/v1/translation/templates", json=sample_translation_template_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == sample_translation_template_data["name"]
        assert data["category"] == sample_translation_template_data["category"]


@pytest.mark.asyncio
async def test_get_translation_templates_paginated(client: AsyncClient):
    """Test getting translation templates with pagination."""
    with patch('src.translation.service.TranslationService.get_translation_templates_paginated') as mock_get:
        mock_templates = [
            {
                "id": "template-1",
                "name": "Greeting Template",
                "description": "Common greetings",
                "source_language": "japanese",
                "target_language": "english",
                "source_pattern": "こんにちは",
                "target_pattern": "Hello",
                "usage_count": 5,
                "category": "greetings",
                "tags": ["common"],
                "average_quality_score": 0.95,
                "created_at": "2025-06-30T10:00:00Z",
                "updated_at": "2025-06-30T10:00:00Z"
            }
        ]
        
        mock_response = {
            "items": mock_templates,
            "meta": {
                "page": 1,
                "limit": 10,
                "total": 1,
                "pages": 1,
                "has_prev": False,
                "has_next": False,
                "prev_page": None,
                "next_page": None
            }
        }
        mock_get.return_value = mock_response
        
        response = await client.get("/api/v1/translation/templates?page=1&limit=10")
        
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert "meta" in data
        assert len(data["items"]) == 1
        assert data["items"][0]["name"] == "Greeting Template"


@pytest.mark.asyncio
async def test_get_translation_statistics(client: AsyncClient):
    """Test getting translation statistics."""
    with patch('src.translation.service.TranslationService.get_translation_statistics') as mock_get_stats:
        mock_stats = {
            "total_jobs": 15,
            "completed_jobs": 12,
            "failed_jobs": 1,
            "pending_jobs": 2,
            "processing_jobs": 0,
            "average_processing_time": 1.8,
            "average_confidence_score": 0.91,
            "average_quality_score": 0.88,
            "language_pairs": {
                "japanese -> english": 10,
                "chinese -> english": 5
            }
        }
        mock_get_stats.return_value = mock_stats
        
        response = await client.get("/api/v1/translation/statistics")
        
        assert response.status_code == 200
        data = response.json()
        assert data["total_jobs"] == 15
        assert data["completed_jobs"] == 12
        assert "language_pairs" in data


@pytest.mark.asyncio
async def test_translation_job_not_found(client: AsyncClient):
    """Test handling of non-existent translation job."""
    job_id = "non-existent-job-id"
    
    with patch('src.translation.service.TranslationService.get_translation_job') as mock_get:
        from src.translation.exceptions import TranslationJobNotFound
        mock_get.side_effect = TranslationJobNotFound("Translation job not found")
        
        response = await client.get(f"/api/v1/translation/jobs/{job_id}")
        
        assert response.status_code == 404
