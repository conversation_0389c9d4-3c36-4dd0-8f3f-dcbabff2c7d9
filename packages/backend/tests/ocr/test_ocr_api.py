"""
Integration tests for OCR API endpoints.
"""
import pytest
from httpx import Async<PERSON><PERSON>
from unittest.mock import AsyncMock, patch


@pytest.mark.asyncio
async def test_create_ocr_job(client: AsyncClient, sample_ocr_job_data):
    """Test creating a new OCR job."""
    with patch('src.ocr.service.OCRService.create_ocr_job') as mock_create:
        mock_response = {
            "id": "test-ocr-job-id",
            "page_id": sample_ocr_job_data["page_id"],
            "status": "pending",
            "provider": sample_ocr_job_data["provider"],
            "prompt_used": sample_ocr_job_data["custom_prompt"],
            "processing_time_seconds": None,
            "error_message": None,
            "retry_count": 0,
            "total_regions_detected": 0,
            "average_confidence": None,
            "created_at": "2025-06-30T10:00:00Z",
            "updated_at": "2025-06-30T10:00:00Z"
        }
        mock_create.return_value = mock_response
        
        response = await client.post("/api/v1/ocr/jobs", json=sample_ocr_job_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["page_id"] == sample_ocr_job_data["page_id"]
        assert data["status"] == "pending"
        assert data["provider"] == sample_ocr_job_data["provider"]


@pytest.mark.asyncio
async def test_get_ocr_job(client: AsyncClient):
    """Test getting an OCR job by ID."""
    job_id = "test-ocr-job-id"
    
    with patch('src.ocr.service.OCRService.get_ocr_job') as mock_get:
        mock_job = {
            "id": job_id,
            "page_id": "test-page-id",
            "status": "completed",
            "provider": "claude",
            "prompt_used": "Extract text from this image",
            "processing_time_seconds": 2.5,
            "error_message": None,
            "retry_count": 0,
            "total_regions_detected": 3,
            "average_confidence": 0.92,
            "created_at": "2025-06-30T10:00:00Z",
            "updated_at": "2025-06-30T10:05:00Z"
        }
        mock_get.return_value = mock_job
        
        response = await client.get(f"/api/v1/ocr/jobs/{job_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == job_id
        assert data["status"] == "completed"
        assert data["total_regions_detected"] == 3


@pytest.mark.asyncio
async def test_get_ocr_job_detail(client: AsyncClient):
    """Test getting detailed OCR job information with results."""
    job_id = "test-ocr-job-id"
    
    with patch('src.ocr.service.OCRService.get_ocr_job_detail') as mock_get_detail:
        mock_detail = {
            "id": job_id,
            "page_id": "test-page-id",
            "status": "completed",
            "provider": "claude",
            "prompt_used": "Extract text from this image",
            "processing_time_seconds": 2.5,
            "error_message": None,
            "retry_count": 0,
            "total_regions_detected": 2,
            "average_confidence": 0.95,
            "created_at": "2025-06-30T10:00:00Z",
            "updated_at": "2025-06-30T10:05:00Z",
            "results": [
                {
                    "id": "result-1",
                    "job_id": job_id,
                    "detected_text": "こんにちは",
                    "confidence_score": 0.98,
                    "region_type": "speech_bubble",
                    "x": 0.1,
                    "y": 0.2,
                    "width": 0.3,
                    "height": 0.1,
                    "metadata": {},
                    "created_at": "2025-06-30T10:05:00Z",
                    "updated_at": "2025-06-30T10:05:00Z"
                },
                {
                    "id": "result-2",
                    "job_id": job_id,
                    "detected_text": "世界",
                    "confidence_score": 0.92,
                    "region_type": "speech_bubble",
                    "x": 0.5,
                    "y": 0.6,
                    "width": 0.2,
                    "height": 0.08,
                    "metadata": {},
                    "created_at": "2025-06-30T10:05:00Z",
                    "updated_at": "2025-06-30T10:05:00Z"
                }
            ]
        }
        mock_get_detail.return_value = mock_detail
        
        response = await client.get(f"/api/v1/ocr/jobs/{job_id}/detail")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == job_id
        assert "results" in data
        assert len(data["results"]) == 2
        assert data["results"][0]["detected_text"] == "こんにちは"


@pytest.mark.asyncio
async def test_get_ocr_results(client: AsyncClient):
    """Test getting OCR results for a job."""
    job_id = "test-ocr-job-id"
    
    with patch('src.ocr.service.OCRService.get_ocr_results') as mock_get_results:
        mock_results = [
            {
                "id": "result-1",
                "job_id": job_id,
                "detected_text": "こんにちは",
                "confidence_score": 0.98,
                "region_type": "speech_bubble",
                "x": 0.1,
                "y": 0.2,
                "width": 0.3,
                "height": 0.1,
                "metadata": {},
                "created_at": "2025-06-30T10:05:00Z",
                "updated_at": "2025-06-30T10:05:00Z"
            }
        ]
        mock_get_results.return_value = mock_results
        
        response = await client.get(f"/api/v1/ocr/jobs/{job_id}/results")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["detected_text"] == "こんにちは"


@pytest.mark.asyncio
async def test_get_page_ocr_jobs(client: AsyncClient):
    """Test getting all OCR jobs for a page."""
    page_id = "test-page-id"
    
    with patch('src.ocr.service.OCRService.get_ocr_jobs_by_page') as mock_get_jobs:
        mock_jobs = [
            {
                "id": "job-1",
                "page_id": page_id,
                "status": "completed",
                "provider": "claude",
                "prompt_used": "Extract text",
                "processing_time_seconds": 2.5,
                "error_message": None,
                "retry_count": 0,
                "total_regions_detected": 3,
                "average_confidence": 0.92,
                "created_at": "2025-06-30T10:00:00Z",
                "updated_at": "2025-06-30T10:05:00Z"
            },
            {
                "id": "job-2",
                "page_id": page_id,
                "status": "failed",
                "provider": "openai",
                "prompt_used": "Extract text",
                "processing_time_seconds": None,
                "error_message": "API rate limit exceeded",
                "retry_count": 1,
                "total_regions_detected": 0,
                "average_confidence": None,
                "created_at": "2025-06-30T11:00:00Z",
                "updated_at": "2025-06-30T11:02:00Z"
            }
        ]
        mock_get_jobs.return_value = mock_jobs
        
        response = await client.get(f"/api/v1/ocr/pages/{page_id}/jobs")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert data[0]["status"] == "completed"
        assert data[1]["status"] == "failed"


@pytest.mark.asyncio
async def test_process_ocr(client: AsyncClient):
    """Test processing OCR request."""
    request_data = {
        "page_id": "test-page-id",
        "provider": "claude",
        "custom_prompt": "Extract all text from this manga page",
        "auto_create_regions": True
    }
    
    with patch('src.ocr.service.OCRService.process_ocr_request') as mock_process:
        mock_response = {
            "id": "new-ocr-job-id",
            "page_id": request_data["page_id"],
            "status": "processing",
            "provider": request_data["provider"],
            "prompt_used": request_data["custom_prompt"],
            "processing_time_seconds": None,
            "error_message": None,
            "retry_count": 0,
            "total_regions_detected": 0,
            "average_confidence": None,
            "created_at": "2025-06-30T10:00:00Z",
            "updated_at": "2025-06-30T10:00:00Z"
        }
        mock_process.return_value = mock_response
        
        response = await client.post("/api/v1/ocr/process", json=request_data)
        
        assert response.status_code == 202  # Accepted
        data = response.json()
        assert data["page_id"] == request_data["page_id"]
        assert data["status"] == "processing"


@pytest.mark.asyncio
async def test_get_ocr_statistics(client: AsyncClient):
    """Test getting OCR statistics."""
    with patch('src.ocr.service.OCRService.get_ocr_statistics') as mock_get_stats:
        mock_stats = {
            "total_jobs": 10,
            "completed_jobs": 7,
            "failed_jobs": 2,
            "pending_jobs": 1,
            "processing_jobs": 0,
            "average_processing_time": 2.3,
            "total_regions_detected": 45,
            "average_confidence": 0.89
        }
        mock_get_stats.return_value = mock_stats
        
        response = await client.get("/api/v1/ocr/statistics")
        
        assert response.status_code == 200
        data = response.json()
        assert data["total_jobs"] == 10
        assert data["completed_jobs"] == 7
        assert data["average_confidence"] == 0.89


@pytest.mark.asyncio
async def test_ocr_job_not_found(client: AsyncClient):
    """Test handling of non-existent OCR job."""
    job_id = "non-existent-job-id"
    
    with patch('src.ocr.service.OCRService.get_ocr_job') as mock_get:
        from src.ocr.exceptions import OCRJobNotFound
        mock_get.side_effect = OCRJobNotFound("OCR job not found")
        
        response = await client.get(f"/api/v1/ocr/jobs/{job_id}")
        
        assert response.status_code == 404


@pytest.mark.asyncio
async def test_retry_ocr_job(client: AsyncClient):
    """Test retrying a failed OCR job."""
    job_id = "failed-ocr-job-id"
    
    with patch('src.ocr.service.OCRService.get_ocr_job') as mock_get:
        mock_job = {
            "id": job_id,
            "page_id": "test-page-id",
            "status": "failed",
            "provider": "claude",
            "prompt_used": "Extract text",
            "processing_time_seconds": None,
            "error_message": "Temporary API error",
            "retry_count": 1,
            "total_regions_detected": 0,
            "average_confidence": None,
            "created_at": "2025-06-30T10:00:00Z",
            "updated_at": "2025-06-30T10:05:00Z"
        }
        mock_get.return_value = mock_job
        
        response = await client.post(f"/api/v1/ocr/jobs/{job_id}/retry")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == job_id
