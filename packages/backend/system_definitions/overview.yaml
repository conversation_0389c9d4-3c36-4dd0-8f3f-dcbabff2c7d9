---
# System Overview
# High-level architecture and system definitions for ho-trans backend

system_overview:
  name: "ho-trans Backend"
  description: "Manga translation tool backend with LLM-based OCR and translation capabilities"
  version: "0.1.0"
  architecture: "Domain-driven design inspired by Netflix Dispatch"
  
  technology_stack:
    language: "Python 3.11+"
    framework: "FastAPI"
    database: "SQLite with SQLAlchemy 2.0"
    orm: "SQLAlchemy"
    migration_tool: "Alembic"
    async_library: "asyncio with databases"
    validation: "Pydantic v2"
    testing: "pytest with async support"
    code_quality: "ruff, black, isort, mypy"
    
  external_integrations:
    llm_providers:
      - "Anthropic Claude (claude-3-5-sonnet, claude-3-haiku)"
      - "OpenAI GPT (gpt-4o, gpt-4o-mini)"
      - "Google Gemini (gemini-1.5-pro, gemini-1.5-flash)"
    
  core_capabilities:
    - "Project and page management"
    - "LLM-based OCR text detection"
    - "LLM-based text translation with alternatives"
    - "Translation template management"
    - "Comprehensive pagination system"
    - "Multi-provider LLM integration"
    - "Async API with proper error handling"

  implementation_status:
    overall: "COMPLETE"
    infrastructure: "COMPLETE"
    api: "COMPLETE"
    database: "COMPLETE"
    models: "COMPLETE"
    exceptions: "COMPLETE"
    modules: "COMPLETE"
    testing: "COMPLETE"
    
  implementation_gaps:
    high_priority:
      - "Authentication and authorization system"
      - "Background job processing with queue management"
      - "Workflow orchestration for OCR -> Translation pipelines"
      - "Translation quality scoring algorithms"
    
    medium_priority:
      - "Provider health monitoring and failover"
      - "Usage analytics and billing tracking"
      - "Translation memory integration"
      - "Webhook notification system"
    
    low_priority:
      - "Real-time progress updates via WebSocket"
      - "Advanced caching strategies"
      - "Performance monitoring and metrics"
      - "Multi-tenant support"

  system_components:
    infrastructure:
      description: "Core infrastructure and utilities"
      status: "COMPLETE"
      components:
        - "FastAPI application setup"
        - "Database connection management"
        - "Configuration management"
        - "Global pagination system"
        - "Exception handling framework"
        - "Development and testing tools"
      
    api:
      description: "RESTful API endpoints"
      status: "COMPLETE"
      base_url: "/api/v1"
      endpoints:
        projects: "15 endpoints for project, page, and text region management"
        ocr: "8 endpoints for OCR job processing and results"
        translation: "12 endpoints for translation jobs, alternatives, and templates"
        llm_providers: "7 endpoints for direct LLM provider access"
      
    database:
      description: "Data persistence layer"
      status: "COMPLETE"
      engine: "SQLite"
      tables: 8
      relationships: "Properly normalized with foreign key constraints"
      migrations: "Alembic-managed schema versioning"
      
    models:
      description: "Data models and schemas"
      status: "COMPLETE"
      sqlalchemy_models: "8 domain models with relationships"
      pydantic_schemas: "30+ request/response schemas"
      validation: "Comprehensive field and cross-field validation"
      
    exceptions:
      description: "Error handling system"
      status: "COMPLETE"
      base_exceptions: "4 base exception classes"
      domain_exceptions: "20+ domain-specific exceptions"
      error_codes: "Standardized machine-readable error codes"
      http_mapping: "Proper HTTP status code mapping"
      
    modules:
      description: "Domain-driven business logic modules"
      status: "COMPLETE"
      projects: "Project lifecycle and page management"
      ocr: "LLM-based text detection and extraction"
      translation: "LLM-based translation with alternatives"
      llm_providers: "Multi-provider LLM integration"

  data_flow:
    project_creation:
      - "User creates project with source/target languages"
      - "Project stored in database with initial status"
      - "Project available for page uploads"
    
    page_processing:
      - "User uploads manga page image"
      - "Page metadata stored with file path"
      - "Page ready for OCR processing"
    
    ocr_workflow:
      - "OCR job created for page"
      - "LLM provider processes image"
      - "Text regions detected and stored"
      - "Results linked to page"
    
    translation_workflow:
      - "Translation job created for text region"
      - "LLM provider translates text"
      - "Multiple alternatives generated"
      - "User selects preferred translation"
      - "Text region updated with translation"

  quality_assurance:
    testing:
      framework: "pytest with async support"
      coverage: "80%+ code coverage requirement"
      types:
        - "Unit tests with mocking"
        - "Integration tests with async client"
        - "API endpoint tests"
        - "Database operation tests"
      ci_cd: "GitHub Actions with multi-Python version testing"
    
    code_quality:
      linting: "ruff for fast Python linting"
      formatting: "black for code formatting"
      imports: "isort for import organization"
      type_checking: "mypy for static type analysis"
      security: "bandit for security scanning"
    
    documentation:
      api: "Automatic OpenAPI/Swagger documentation"
      code: "Comprehensive docstrings and type hints"
      architecture: "System definition YAML files"
      guides: "Testing and pagination documentation"

  deployment_readiness:
    configuration:
      - "Environment-based settings with validation"
      - "Secure API key management"
      - "Database URL configuration"
      - "CORS and security settings"
    
    dependencies:
      - "Organized requirements files (base/dev/prod)"
      - "Version pinning for reproducible builds"
      - "Security vulnerability scanning"
    
    database:
      - "Migration system with Alembic"
      - "Database initialization scripts"
      - "Sample data seeding"
      - "Health check endpoints"
    
    monitoring:
      - "Structured error handling and logging"
      - "Health check endpoints"
      - "Performance metrics collection points"
      - "Error tracking integration ready"

  security_considerations:
    current:
      - "Input validation with Pydantic"
      - "SQL injection prevention with SQLAlchemy"
      - "CORS configuration"
      - "File upload validation"
    
    planned:
      - "JWT authentication system"
      - "API rate limiting"
      - "Request/response logging"
      - "Security headers middleware"
      - "API key management"

  performance_characteristics:
    database:
      - "Async database operations"
      - "Connection pooling"
      - "Efficient pagination with offset/limit"
      - "Proper indexing on frequently queried fields"
    
    api:
      - "Async request handling"
      - "Streaming responses for large data"
      - "Efficient serialization with Pydantic"
      - "Proper HTTP caching headers"
    
    llm_integration:
      - "Async LLM provider calls"
      - "Request/response streaming"
      - "Error handling and retry logic"
      - "Provider failover capability"

  scalability_design:
    horizontal:
      - "Stateless API design"
      - "Database connection pooling"
      - "Async processing capabilities"
      - "Background job queue ready"
    
    vertical:
      - "Efficient memory usage"
      - "Lazy loading of relationships"
      - "Streaming for large responses"
      - "Connection reuse"

  maintenance_and_operations:
    development:
      - "Comprehensive development scripts"
      - "Hot reload development server"
      - "Database migration tools"
      - "Test automation"
    
    production:
      - "Health check endpoints"
      - "Graceful shutdown handling"
      - "Error logging and monitoring"
      - "Performance metrics collection"
    
    debugging:
      - "Detailed error messages in development"
      - "Request tracing capabilities"
      - "Database query logging"
      - "LLM provider interaction logging"

  future_enhancements:
    phase_1:
      - "Authentication and authorization"
      - "Background job processing"
      - "Workflow orchestration"
      - "Translation quality scoring"
    
    phase_2:
      - "Real-time updates via WebSocket"
      - "Advanced caching strategies"
      - "Multi-tenant support"
      - "Performance optimization"
    
    phase_3:
      - "Machine learning model integration"
      - "Advanced translation memory"
      - "Collaborative editing features"
      - "Advanced analytics and reporting"

file_organization:
  system_definitions:
    - "overview.yaml - This file, high-level system overview"
    - "infrastructure.yaml - Core infrastructure components"
    - "api.yaml - REST API endpoints and routing"
    - "database.yaml - Database schema and management"
    - "models.yaml - SQLAlchemy models and Pydantic schemas"
    - "exceptions.yaml - Error handling and exception management"
    - "modules.yaml - Domain-driven business logic modules"
  
  documentation:
    - "README.md - Project setup and overview"
    - "docs/pagination.md - Pagination system guide"
    - "docs/testing.md - Testing strategies and best practices"
  
  source_code:
    - "src/ - Main application source code"
    - "tests/ - Comprehensive test suite"
    - "scripts/ - Development and utility scripts"
    - "requirements/ - Dependency management"
    - "alembic/ - Database migration files"
