---
# Infrastructure System Definitions
# Core infrastructure components and configuration

infrastructure:
  description: "Core infrastructure components for ho-trans backend application"
  intent: "Provide foundational services, configuration, and utilities for the application"
  
  components:
    application:
      description: "FastAPI application setup and configuration"
      intent: "Main application entry point with middleware, CORS, and router configuration"
      implementation_status: "COMPLETE"
      implementation_gaps: []
      depends_on: []
      files:
        - "src/main.py"
        - "src/config.py"
      features:
        - "FastAPI application factory"
        - "CORS middleware configuration"
        - "Lifespan event management"
        - "Router registration"
        - "Environment-based configuration"
    
    configuration:
      description: "Application configuration management using Pydantic Settings"
      intent: "Centralized configuration with environment variable support and validation"
      implementation_status: "COMPLETE"
      implementation_gaps: []
      depends_on: []
      files:
        - "src/config.py"
        - ".env.example"
      features:
        - "Environment-based settings"
        - "LLM provider API key management"
        - "Database configuration"
        - "CORS settings"
        - "File upload limits"
        - "Default language settings"
    
    database:
      description: "Database connection and SQLAlchemy setup"
      intent: "Provide async database connectivity with SQLAlchemy ORM integration"
      implementation_status: "COMPLETE"
      implementation_gaps: []
      depends_on: []
      files:
        - "src/database.py"
        - "src/models.py"
      features:
        - "Async database connection using databases library"
        - "SQLAlchemy Base model with UUID primary keys"
        - "Automatic timestamp management"
        - "Database dependency injection"
    
    pagination:
      description: "Global pagination system for consistent API responses"
      intent: "Provide standardized pagination across all modules with metadata"
      implementation_status: "COMPLETE"
      implementation_gaps: []
      depends_on:
        - "database"
      files:
        - "src/pagination.py"
      features:
        - "PaginationParams with automatic offset calculation"
        - "Generic PaginatedResponse wrapper"
        - "Database Paginator utility"
        - "FastAPI dependency integration"
        - "Search and filtering support"
    
    schemas:
      description: "Base Pydantic schemas and response models"
      intent: "Provide common schema patterns and response structures"
      implementation_status: "COMPLETE"
      implementation_gaps: []
      depends_on: []
      files:
        - "src/schemas.py"
      features:
        - "CustomModel base class with alias generation"
        - "BaseSchema with common fields"
        - "SuccessResponse for operation confirmations"
        - "ErrorResponse for error handling"
    
    exceptions:
      description: "Global exception handling and custom exception classes"
      intent: "Provide consistent error handling across the application"
      implementation_status: "COMPLETE"
      implementation_gaps: []
      depends_on: []
      files:
        - "src/exceptions.py"
      features:
        - "Base exception classes with error codes"
        - "HTTP status code mapping"
        - "Structured error responses"
        - "Domain-specific exception inheritance"
    
    constants:
      description: "Application-wide constants and enumerations"
      intent: "Centralize all constants, enums, and configuration values"
      implementation_status: "COMPLETE"
      implementation_gaps: []
      depends_on: []
      files:
        - "src/constants.py"
      features:
        - "Status enumerations for all domains"
        - "LLM provider definitions"
        - "Language support constants"
        - "Default prompts and templates"
        - "Error code definitions"

  development_tools:
    description: "Development and testing infrastructure"
    intent: "Provide comprehensive tooling for development, testing, and deployment"
    
    components:
      testing:
        description: "Comprehensive testing framework with async support"
        intent: "Ensure code quality and prevent regressions with proper async testing"
        implementation_status: "COMPLETE"
        implementation_gaps: []
        depends_on:
          - "application"
          - "database"
        files:
          - "tests/conftest.py"
          - "tests/utils.py"
          - "pytest.ini"
          - "pyproject.toml"
        features:
          - "Async test client setup (prevents event loop issues)"
          - "Mock database utilities"
          - "Test fixtures for all domains"
          - "Coverage reporting (80% minimum)"
          - "Test markers and categorization"
      
      database_management:
        description: "Database initialization and migration tools"
        intent: "Manage database schema and provide development utilities"
        implementation_status: "COMPLETE"
        implementation_gaps: []
        depends_on:
          - "database"
        files:
          - "src/database_init.py"
          - "alembic.ini"
          - "alembic/env.py"
          - "alembic/versions/"
        features:
          - "Database initialization script"
          - "Alembic migration support"
          - "Sample data seeding"
          - "Database health checks"
      
      development_scripts:
        description: "Development automation scripts"
        intent: "Streamline common development tasks and workflows"
        implementation_status: "COMPLETE"
        implementation_gaps: []
        depends_on: []
        files:
          - "scripts/dev.py"
          - "scripts/test.py"
          - "Makefile"
        features:
          - "Development server management"
          - "Code quality checks (linting, formatting, type checking)"
          - "Test execution with various options"
          - "Database management commands"
          - "Documentation generation"
      
      code_quality:
        description: "Code quality and formatting tools configuration"
        intent: "Maintain consistent code style and quality standards"
        implementation_status: "COMPLETE"
        implementation_gaps: []
        depends_on: []
        files:
          - "pyproject.toml"
          - "requirements/dev.txt"
        features:
          - "Ruff linting configuration"
          - "Black code formatting"
          - "isort import sorting"
          - "MyPy type checking"
          - "Pre-commit hooks ready"

  deployment:
    description: "Deployment and production configuration"
    intent: "Support various deployment scenarios with proper configuration"
    
    components:
      requirements:
        description: "Dependency management with environment separation"
        intent: "Organize dependencies by environment with proper versioning"
        implementation_status: "COMPLETE"
        implementation_gaps: []
        depends_on: []
        files:
          - "requirements/base.txt"
          - "requirements/dev.txt"
          - "requirements/prod.txt"
        features:
          - "Base dependencies for core functionality"
          - "Development dependencies for testing and tooling"
          - "Production dependencies for deployment"
          - "Version pinning for reproducible builds"
      
      ci_cd:
        description: "Continuous integration and deployment pipeline"
        intent: "Automate testing, quality checks, and deployment processes"
        implementation_status: "COMPLETE"
        implementation_gaps: []
        depends_on:
          - "testing"
          - "code_quality"
        files:
          - ".github/workflows/test.yml"
        features:
          - "Multi-Python version testing"
          - "Parallel job execution"
          - "Code quality gates"
          - "Security scanning"
          - "Coverage reporting"
          - "Database migration testing"

  documentation:
    description: "Comprehensive project documentation"
    intent: "Provide clear guidance for development, testing, and deployment"
    implementation_status: "COMPLETE"
    implementation_gaps: []
    depends_on: []
    files:
      - "README.md"
      - "docs/pagination.md"
      - "docs/testing.md"
    features:
      - "Setup and installation guides"
      - "API documentation"
      - "Testing strategies and best practices"
      - "Architecture documentation"
      - "Development workflows"
