---
# Exceptions System Definitions
# Error handling and exception management

exceptions:
  description: "Comprehensive exception handling system for ho-trans backend"
  intent: "Provide structured error handling with consistent error responses and proper HTTP status codes"
  
  base_exceptions:
    description: "Base exception classes providing common functionality"
    intent: "Establish consistent error handling patterns across all modules"
    implementation_status: "COMPLETE"
    implementation_gaps: []
    depends_on: []
    files:
      - "src/exceptions.py"
    
    components:
      base_exception:
        description: "Root exception class for all application errors"
        intent: "Provide common error structure and HTTP status mapping"
        class_name: "BaseException"
        features:
          - "Error code assignment"
          - "HTTP status code mapping"
          - "Structured error messages"
          - "Exception chaining support"
          - "Logging integration"
        fields:
          - name: "message"
            type: "str"
            description: "Human-readable error message"
          - name: "error_code"
            type: "str"
            description: "Machine-readable error code"
          - name: "status_code"
            type: "int"
            description: "HTTP status code"
          - name: "details"
            type: "Optional[Dict[str, Any]]"
            description: "Additional error details"
      
      validation_exception:
        description: "Base class for validation errors"
        intent: "Handle data validation failures"
        class_name: "ValidationException"
        inherits: "BaseException"
        status_code: 422
        features:
          - "Field-specific error messages"
          - "Multiple validation error aggregation"
          - "Pydantic integration"
      
      not_found_exception:
        description: "Base class for resource not found errors"
        intent: "Handle missing resource scenarios"
        class_name: "NotFoundException"
        inherits: "BaseException"
        status_code: 404
        features:
          - "Resource type identification"
          - "Resource ID tracking"
          - "Consistent not found messaging"
      
      conflict_exception:
        description: "Base class for resource conflict errors"
        intent: "Handle resource conflicts and constraint violations"
        class_name: "ConflictException"
        inherits: "BaseException"
        status_code: 409
        features:
          - "Conflict type identification"
          - "Constraint violation details"
          - "Resolution suggestions"

  domain_exceptions:
    projects:
      description: "Project management specific exceptions"
      intent: "Handle project-related error scenarios"
      implementation_status: "COMPLETE"
      implementation_gaps: []
      depends_on:
        - "base_exceptions"
      files:
        - "src/projects/exceptions.py"
      
      exceptions:
        project_not_found:
          description: "Project does not exist"
          class_name: "ProjectNotFound"
          inherits: "NotFoundException"
          error_code: "PROJECT_NOT_FOUND"
          status_code: 404
          message_template: "Project with ID '{project_id}' not found"
        
        project_page_not_found:
          description: "Project page does not exist"
          class_name: "ProjectPageNotFound"
          inherits: "NotFoundException"
          error_code: "PROJECT_PAGE_NOT_FOUND"
          status_code: 404
          message_template: "Page with ID '{page_id}' not found in project '{project_id}'"
        
        text_region_not_found:
          description: "Text region does not exist"
          class_name: "TextRegionNotFound"
          inherits: "NotFoundException"
          error_code: "TEXT_REGION_NOT_FOUND"
          status_code: 404
          message_template: "Text region with ID '{region_id}' not found"
        
        project_name_conflict:
          description: "Project name already exists"
          class_name: "ProjectNameConflict"
          inherits: "ConflictException"
          error_code: "PROJECT_NAME_CONFLICT"
          status_code: 409
          message_template: "Project with name '{name}' already exists"
        
        page_number_conflict:
          description: "Page number already exists in project"
          class_name: "PageNumberConflict"
          inherits: "ConflictException"
          error_code: "PAGE_NUMBER_CONFLICT"
          status_code: 409
          message_template: "Page number {page_number} already exists in project '{project_id}'"
        
        invalid_file_format:
          description: "Uploaded file format is not supported"
          class_name: "InvalidFileFormat"
          inherits: "ValidationException"
          error_code: "INVALID_FILE_FORMAT"
          status_code: 422
          message_template: "File format '{format}' is not supported. Supported formats: {supported_formats}"
        
        file_too_large:
          description: "Uploaded file exceeds size limit"
          class_name: "FileTooLarge"
          inherits: "ValidationException"
          error_code: "FILE_TOO_LARGE"
          status_code: 422
          message_template: "File size {size} exceeds maximum limit of {max_size}"

    ocr:
      description: "OCR processing specific exceptions"
      intent: "Handle OCR-related error scenarios"
      implementation_status: "COMPLETE"
      implementation_gaps: []
      depends_on:
        - "base_exceptions"
      files:
        - "src/ocr/exceptions.py"
      
      exceptions:
        ocr_job_not_found:
          description: "OCR job does not exist"
          class_name: "OCRJobNotFound"
          inherits: "NotFoundException"
          error_code: "OCR_JOB_NOT_FOUND"
          status_code: 404
          message_template: "OCR job with ID '{job_id}' not found"
        
        ocr_result_not_found:
          description: "OCR result does not exist"
          class_name: "OCRResultNotFound"
          inherits: "NotFoundException"
          error_code: "OCR_RESULT_NOT_FOUND"
          status_code: 404
          message_template: "OCR result with ID '{result_id}' not found"
        
        ocr_processing_failed:
          description: "OCR processing failed"
          class_name: "OCRProcessingFailed"
          inherits: "BaseException"
          error_code: "OCR_PROCESSING_FAILED"
          status_code: 500
          message_template: "OCR processing failed: {error_details}"
        
        ocr_provider_unavailable:
          description: "OCR provider is not available"
          class_name: "OCRProviderUnavailable"
          inherits: "BaseException"
          error_code: "OCR_PROVIDER_UNAVAILABLE"
          status_code: 503
          message_template: "OCR provider '{provider}' is currently unavailable"
        
        invalid_ocr_status:
          description: "Invalid OCR job status transition"
          class_name: "InvalidOCRStatus"
          inherits: "ValidationException"
          error_code: "INVALID_OCR_STATUS"
          status_code: 422
          message_template: "Cannot transition OCR job from '{current_status}' to '{new_status}'"

    translation:
      description: "Translation processing specific exceptions"
      intent: "Handle translation-related error scenarios"
      implementation_status: "COMPLETE"
      implementation_gaps: []
      depends_on:
        - "base_exceptions"
      files:
        - "src/translation/exceptions.py"
      
      exceptions:
        translation_job_not_found:
          description: "Translation job does not exist"
          class_name: "TranslationJobNotFound"
          inherits: "NotFoundException"
          error_code: "TRANSLATION_JOB_NOT_FOUND"
          status_code: 404
          message_template: "Translation job with ID '{job_id}' not found"
        
        translation_alternative_not_found:
          description: "Translation alternative does not exist"
          class_name: "TranslationAlternativeNotFound"
          inherits: "NotFoundException"
          error_code: "TRANSLATION_ALTERNATIVE_NOT_FOUND"
          status_code: 404
          message_template: "Translation alternative with ID '{alternative_id}' not found"
        
        translation_template_not_found:
          description: "Translation template does not exist"
          class_name: "TranslationTemplateNotFound"
          inherits: "NotFoundException"
          error_code: "TRANSLATION_TEMPLATE_NOT_FOUND"
          status_code: 404
          message_template: "Translation template with ID '{template_id}' not found"
        
        translation_failed:
          description: "Translation processing failed"
          class_name: "TranslationFailed"
          inherits: "BaseException"
          error_code: "TRANSLATION_FAILED"
          status_code: 500
          message_template: "Translation failed: {error_details}"
        
        unsupported_language_pair:
          description: "Language pair is not supported"
          class_name: "UnsupportedLanguagePair"
          inherits: "ValidationException"
          error_code: "UNSUPPORTED_LANGUAGE_PAIR"
          status_code: 422
          message_template: "Language pair '{source}' -> '{target}' is not supported"
        
        template_name_conflict:
          description: "Translation template name already exists"
          class_name: "TemplateNameConflict"
          inherits: "ConflictException"
          error_code: "TEMPLATE_NAME_CONFLICT"
          status_code: 409
          message_template: "Translation template with name '{name}' already exists"

    llm_providers:
      description: "LLM provider specific exceptions"
      intent: "Handle LLM provider integration errors"
      implementation_status: "COMPLETE"
      implementation_gaps: []
      depends_on:
        - "base_exceptions"
      files:
        - "src/llm_providers/exceptions.py"
      
      exceptions:
        llm_provider_not_available:
          description: "LLM provider is not available"
          class_name: "LLMProviderNotAvailable"
          inherits: "BaseException"
          error_code: "LLM_PROVIDER_NOT_AVAILABLE"
          status_code: 503
          message_template: "LLM provider '{provider}' is not available"
        
        llm_api_error:
          description: "LLM API returned an error"
          class_name: "LLMAPIError"
          inherits: "BaseException"
          error_code: "LLM_API_ERROR"
          status_code: 502
          message_template: "LLM API error: {error_details}"
        
        llm_rate_limit_exceeded:
          description: "LLM provider rate limit exceeded"
          class_name: "LLMRateLimitExceeded"
          inherits: "BaseException"
          error_code: "LLM_RATE_LIMIT_EXCEEDED"
          status_code: 429
          message_template: "Rate limit exceeded for provider '{provider}'. Retry after {retry_after} seconds"
        
        llm_quota_exceeded:
          description: "LLM provider quota exceeded"
          class_name: "LLMQuotaExceeded"
          inherits: "BaseException"
          error_code: "LLM_QUOTA_EXCEEDED"
          status_code: 402
          message_template: "Quota exceeded for provider '{provider}'"
        
        invalid_llm_model:
          description: "Requested LLM model is not available"
          class_name: "InvalidLLMModel"
          inherits: "ValidationException"
          error_code: "INVALID_LLM_MODEL"
          status_code: 422
          message_template: "Model '{model}' is not available for provider '{provider}'"
        
        llm_timeout:
          description: "LLM request timed out"
          class_name: "LLMTimeout"
          inherits: "BaseException"
          error_code: "LLM_TIMEOUT"
          status_code: 504
          message_template: "LLM request timed out after {timeout} seconds"

  error_handling:
    description: "Global error handling and middleware"
    intent: "Provide consistent error responses and logging"
    implementation_status: "COMPLETE"
    implementation_gaps: []
    depends_on:
      - "base_exceptions"
      - "domain_exceptions"
    files:
      - "src/main.py"
    
    components:
      exception_handler:
        description: "Global exception handler for FastAPI"
        intent: "Convert exceptions to proper HTTP responses"
        features:
          - "Automatic status code mapping"
          - "Structured error response format"
          - "Error logging and tracking"
          - "Development vs production error details"
          - "Request context preservation"
      
      validation_error_handler:
        description: "Pydantic validation error handler"
        intent: "Convert validation errors to user-friendly messages"
        features:
          - "Field-specific error messages"
          - "Error aggregation"
          - "Localization support"
          - "Custom validation error formatting"
      
      http_exception_handler:
        description: "HTTP exception handler"
        intent: "Handle FastAPI HTTP exceptions"
        features:
          - "Consistent error response format"
          - "Status code preservation"
          - "Error detail formatting"

  error_codes:
    description: "Standardized error codes for all exceptions"
    intent: "Provide machine-readable error identification"
    implementation_status: "COMPLETE"
    implementation_gaps: []
    depends_on: []
    files:
      - "src/constants.py"
    
    categories:
      general:
        - "INTERNAL_SERVER_ERROR"
        - "VALIDATION_ERROR"
        - "NOT_FOUND"
        - "CONFLICT"
        - "UNAUTHORIZED"
        - "FORBIDDEN"
      
      projects:
        - "PROJECT_NOT_FOUND"
        - "PROJECT_PAGE_NOT_FOUND"
        - "TEXT_REGION_NOT_FOUND"
        - "PROJECT_NAME_CONFLICT"
        - "PAGE_NUMBER_CONFLICT"
        - "INVALID_FILE_FORMAT"
        - "FILE_TOO_LARGE"
      
      ocr:
        - "OCR_JOB_NOT_FOUND"
        - "OCR_RESULT_NOT_FOUND"
        - "OCR_PROCESSING_FAILED"
        - "OCR_PROVIDER_UNAVAILABLE"
        - "INVALID_OCR_STATUS"
      
      translation:
        - "TRANSLATION_JOB_NOT_FOUND"
        - "TRANSLATION_ALTERNATIVE_NOT_FOUND"
        - "TRANSLATION_TEMPLATE_NOT_FOUND"
        - "TRANSLATION_FAILED"
        - "UNSUPPORTED_LANGUAGE_PAIR"
        - "TEMPLATE_NAME_CONFLICT"
      
      llm_providers:
        - "LLM_PROVIDER_NOT_AVAILABLE"
        - "LLM_API_ERROR"
        - "LLM_RATE_LIMIT_EXCEEDED"
        - "LLM_QUOTA_EXCEEDED"
        - "INVALID_LLM_MODEL"
        - "LLM_TIMEOUT"

  logging:
    description: "Error logging and monitoring integration"
    intent: "Track and monitor application errors"
    implementation_status: "PARTIAL"
    implementation_gaps:
      - "Structured logging configuration"
      - "Error tracking service integration"
      - "Performance monitoring"
      - "Alert configuration"
    depends_on:
      - "error_handling"
    planned_features:
      - "Structured JSON logging"
      - "Error aggregation and reporting"
      - "Performance metrics collection"
      - "Real-time error alerts"
      - "Error trend analysis"
