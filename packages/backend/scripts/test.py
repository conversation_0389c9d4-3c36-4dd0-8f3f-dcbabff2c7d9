#!/usr/bin/env python3
"""
Test runner script for ho-trans backend.
"""
import subprocess
import sys
from pathlib import Path


def run_command(command: str, cwd: Path = None):
    """Run a shell command."""
    print(f"Running: {command}")
    result = subprocess.run(command, shell=True, cwd=cwd)
    return result.returncode


def run_all_tests():
    """Run all tests."""
    return run_command("pytest tests/ -v")


def run_unit_tests():
    """Run unit tests only."""
    return run_command("pytest tests/ -v -m 'not integration'")


def run_integration_tests():
    """Run integration tests only."""
    return run_command("pytest tests/ -v -m integration")


def run_coverage():
    """Run tests with coverage report."""
    return run_command("pytest tests/ -v --cov=src --cov-report=html --cov-report=term")


def run_specific_module(module: str):
    """Run tests for a specific module."""
    return run_command(f"pytest tests/{module}/ -v")


def run_specific_test(test_path: str):
    """Run a specific test."""
    return run_command(f"pytest {test_path} -v")


def run_parallel():
    """Run tests in parallel."""
    return run_command("pytest tests/ -v -n auto")


def run_with_markers(markers: str):
    """Run tests with specific markers."""
    return run_command(f"pytest tests/ -v -m '{markers}'")


def check_test_setup():
    """Check if test environment is properly set up."""
    print("Checking test environment...")
    
    # Check if pytest is installed
    result = run_command("python -c 'import pytest; print(pytest.__version__)'")
    if result != 0:
        print("❌ pytest not installed")
        return False
    
    # Check if async dependencies are available
    result = run_command("python -c 'import httpx, pytest_asyncio'")
    if result != 0:
        print("❌ async test dependencies not installed")
        return False
    
    # Check if source code is importable
    result = run_command("python -c 'import src.main'")
    if result != 0:
        print("❌ source code not importable")
        return False
    
    print("✅ Test environment is properly set up")
    return True


def main():
    """Main entry point for test runner."""
    if len(sys.argv) < 2:
        print("Usage: python scripts/test.py <command>")
        print("\nAvailable commands:")
        print("  all         - Run all tests")
        print("  unit        - Run unit tests only")
        print("  integration - Run integration tests only")
        print("  coverage    - Run tests with coverage report")
        print("  parallel    - Run tests in parallel")
        print("  check       - Check test environment setup")
        print("  module <name> - Run tests for specific module")
        print("  test <path>   - Run specific test")
        print("  markers <markers> - Run tests with specific markers")
        print("\nExamples:")
        print("  python scripts/test.py all")
        print("  python scripts/test.py module projects")
        print("  python scripts/test.py test tests/test_pagination.py")
        print("  python scripts/test.py markers 'unit and not slow'")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "all":
        exit_code = run_all_tests()
    elif command == "unit":
        exit_code = run_unit_tests()
    elif command == "integration":
        exit_code = run_integration_tests()
    elif command == "coverage":
        exit_code = run_coverage()
    elif command == "parallel":
        exit_code = run_parallel()
    elif command == "check":
        success = check_test_setup()
        exit_code = 0 if success else 1
    elif command == "module":
        if len(sys.argv) < 3:
            print("Usage: python scripts/test.py module <module_name>")
            sys.exit(1)
        module = sys.argv[2]
        exit_code = run_specific_module(module)
    elif command == "test":
        if len(sys.argv) < 3:
            print("Usage: python scripts/test.py test <test_path>")
            sys.exit(1)
        test_path = sys.argv[2]
        exit_code = run_specific_test(test_path)
    elif command == "markers":
        if len(sys.argv) < 3:
            print("Usage: python scripts/test.py markers '<marker_expression>'")
            sys.exit(1)
        markers = sys.argv[2]
        exit_code = run_with_markers(markers)
    else:
        print(f"Unknown command: {command}")
        sys.exit(1)
    
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
