#!/usr/bin/env python3
"""
Development scripts for ho-trans backend.
"""
import asyncio
import subprocess
import sys
from pathlib import Path


def run_command(command: str, cwd: Path = None):
    """Run a shell command."""
    print(f"Running: {command}")
    result = subprocess.run(command, shell=True, cwd=cwd)
    if result.returncode != 0:
        print(f"Command failed with exit code {result.returncode}")
        sys.exit(1)


def install_dependencies():
    """Install development dependencies."""
    print("Installing development dependencies...")
    run_command("pip install -r requirements/dev.txt")


def run_server():
    """Run the development server."""
    print("Starting development server...")
    run_command("uvicorn src.main:app --reload --host 0.0.0.0 --port 8000")


def run_tests():
    """Run tests."""
    print("Running tests...")
    run_command("pytest tests/ -v --cov=src --cov-report=html")


def lint_code():
    """Run code linting."""
    print("Running code linting...")
    run_command("ruff check src/")
    run_command("black --check src/")
    run_command("isort --check-only src/")


def format_code():
    """Format code."""
    print("Formatting code...")
    run_command("ruff check --fix src/")
    run_command("black src/")
    run_command("isort src/")


def type_check():
    """Run type checking."""
    print("Running type checking...")
    run_command("mypy src/")


def init_db():
    """Initialize database."""
    print("Initializing database...")
    run_command("python src/database_init.py init")


def reset_db():
    """Reset database."""
    print("Resetting database...")
    run_command("python src/database_init.py reset")


def check_db():
    """Check database."""
    print("Checking database...")
    run_command("python src/database_init.py check")


def seed_db():
    """Seed database with sample data."""
    print("Seeding database...")
    run_command("python src/database_init.py seed")


def create_migration(message: str):
    """Create a new migration."""
    print(f"Creating migration: {message}")
    run_command(f'alembic revision --autogenerate -m "{message}"')


def run_migrations():
    """Run database migrations."""
    print("Running migrations...")
    run_command("alembic upgrade head")


def generate_docs():
    """Generate documentation."""
    print("Generating documentation...")
    run_command("mkdocs build")


def serve_docs():
    """Serve documentation."""
    print("Serving documentation...")
    run_command("mkdocs serve")


def clean():
    """Clean up generated files."""
    print("Cleaning up...")
    paths_to_clean = [
        "**/__pycache__",
        "**/*.pyc",
        "**/*.pyo",
        ".coverage",
        "htmlcov/",
        ".pytest_cache/",
        ".mypy_cache/",
        "dist/",
        "build/",
        "*.egg-info/",
    ]
    
    for pattern in paths_to_clean:
        run_command(f"find . -name '{pattern}' -exec rm -rf {{}} + 2>/dev/null || true")


def main():
    """Main entry point for development scripts."""
    if len(sys.argv) < 2:
        print("Usage: python scripts/dev.py <command>")
        print("\nAvailable commands:")
        print("  install     - Install development dependencies")
        print("  server      - Run development server")
        print("  test        - Run tests")
        print("  lint        - Run code linting")
        print("  format      - Format code")
        print("  typecheck   - Run type checking")
        print("  init-db     - Initialize database")
        print("  reset-db    - Reset database")
        print("  check-db    - Check database")
        print("  seed-db     - Seed database with sample data")
        print("  migrate     - Run database migrations")
        print("  migration <message> - Create new migration")
        print("  docs        - Generate documentation")
        print("  serve-docs  - Serve documentation")
        print("  clean       - Clean up generated files")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "install":
        install_dependencies()
    elif command == "server":
        run_server()
    elif command == "test":
        run_tests()
    elif command == "lint":
        lint_code()
    elif command == "format":
        format_code()
    elif command == "typecheck":
        type_check()
    elif command == "init-db":
        init_db()
    elif command == "reset-db":
        reset_db()
    elif command == "check-db":
        check_db()
    elif command == "seed-db":
        seed_db()
    elif command == "migrate":
        run_migrations()
    elif command == "migration":
        if len(sys.argv) < 3:
            print("Usage: python scripts/dev.py migration <message>")
            sys.exit(1)
        create_migration(sys.argv[2])
    elif command == "docs":
        generate_docs()
    elif command == "serve-docs":
        serve_docs()
    elif command == "clean":
        clean()
    else:
        print(f"Unknown command: {command}")
        sys.exit(1)


if __name__ == "__main__":
    main()
