"""Allow transparent background color for text regions

Revision ID: 1d185b05d524
Revises: 30aad5358760
Create Date: 2025-06-30 15:27:38.306768

"""
# No imports needed for this migration


# revision identifiers, used by Alembic.
revision = '1d185b05d524'
down_revision = '30aad5358760'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # SQLite doesn't support ALTER COLUMN TYPE, so we need to recreate the table
    # Since this is a length increase and we're adding support for 'transparent',
    # we can safely skip this migration for SQLite as the constraint is only
    # enforced at the application level via Pydantic validation
    pass


def downgrade() -> None:
    # No changes needed for downgrade since upgrade is a no-op
    pass
