"""
Deepseek LLM provider client.
"""
import base64
from typing import List, Optional

import httpx

from src.constants import LLMProvider
from src.llm_providers.base import (
    BaseLLMClient, LLMResponse, LLMMessage,
    LLMAPIError, LLMRateLimitError, LLMAuthenticationError, LLMValidationError
)


class DeepseekClient(BaseLLMClient):
    """Deepseek API client."""

    BASE_URL = "https://api.deepseek.com/v1"
    DEFAULT_MODEL = "deepseek-chat"

    AVAILABLE_MODELS = [
        "deepseek-chat",
        "deepseek-reasoner"
    ]

    def __init__(self, api_key: str, **kwargs):
        super().__init__(api_key, **kwargs)
        self.client = httpx.AsyncClient(
            base_url=self.BASE_URL,
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            },
            timeout=60.0
        )

    async def generate_text(
        self,
        messages: List[LLMMessage],
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        **kwargs
    ) -> LLMResponse:
        """Generate text using Deepseek."""
        model = model or self.DEFAULT_MODEL
        temperature = temperature or 0.7

        # Prepare messages for Deepseek format (OpenAI-compatible)
        deepseek_messages = self.prepare_messages(messages)

        payload = {
            "model": model,
            "messages": deepseek_messages,
            "temperature": temperature,
            **kwargs
        }

        if max_tokens:
            payload["max_tokens"] = max_tokens

        try:
            response = await self.client.post("/chat/completions", json=payload)
            response.raise_for_status()

            data = response.json()
            choice = data["choices"][0]

            return LLMResponse(
                content=choice["message"]["content"],
                provider=LLMProvider.DEEPSEEK,
                model=model,
                usage=data.get("usage"),
                metadata={
                    "finish_reason": choice.get("finish_reason"),
                    "index": choice.get("index")
                },
                raw_response=data
            )

        except httpx.HTTPStatusError as e:
            await self._handle_http_error(e)
        except Exception as e:
            raise LLMAPIError(
                f"Deepseek API error: {str(e)}", LLMProvider.DEEPSEEK)

    async def generate_text_with_image(
        self,
        messages: List[LLMMessage],
        image_data: bytes,
        image_format: str,
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        **kwargs
    ) -> LLMResponse:
        """Generate text with image input using Deepseek."""
        model = model or self.DEFAULT_MODEL
        temperature = temperature or 0.7

        # Encode image to base64
        image_base64 = base64.b64encode(image_data).decode('utf-8')

        # Prepare messages with image (OpenAI-compatible format)
        deepseek_messages = []

        for msg in messages:
            if msg.role == "user":
                # Add image to the first user message
                deepseek_messages.append({
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": msg.content
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/{image_format};base64,{image_base64}"
                            }
                        }
                    ]
                })
            else:
                deepseek_messages.append({
                    "role": msg.role,
                    "content": msg.content
                })

        payload = {
            "model": model,
            "messages": deepseek_messages,
            "temperature": temperature,
            **kwargs
        }

        if max_tokens:
            payload["max_tokens"] = max_tokens

        try:
            response = await self.client.post("/chat/completions", json=payload)
            response.raise_for_status()

            data = response.json()
            choice = data["choices"][0]

            return LLMResponse(
                content=choice["message"]["content"],
                provider=LLMProvider.DEEPSEEK,
                model=model,
                usage=data.get("usage"),
                metadata={
                    "finish_reason": choice.get("finish_reason"),
                    "index": choice.get("index"),
                    "image_format": image_format
                },
                raw_response=data
            )

        except httpx.HTTPStatusError as e:
            await self._handle_http_error(e)
        except Exception as e:
            raise LLMAPIError(
                f"Deepseek API error: {str(e)}", LLMProvider.DEEPSEEK)

    async def _handle_http_error(self, error: httpx.HTTPStatusError):
        """Handle HTTP errors from Deepseek API."""
        status_code = error.response.status_code

        try:
            error_data = error.response.json()
            error_message = error_data.get(
                "error", {}).get("message", str(error))
            error_type = error_data.get("error", {}).get("type", "unknown")
        except:
            error_message = str(error)
            error_type = "unknown"

        if status_code == 401:
            raise LLMAuthenticationError(
                f"Deepseek authentication error: {error_message}", LLMProvider.DEEPSEEK)
        elif status_code == 429:
            raise LLMRateLimitError(
                f"Deepseek rate limit exceeded: {error_message}", LLMProvider.DEEPSEEK)
        elif status_code == 400:
            raise LLMValidationError(
                f"Deepseek validation error: {error_message}", LLMProvider.DEEPSEEK)
        else:
            raise LLMAPIError(
                f"Deepseek API error ({status_code}): {error_message}", LLMProvider.DEEPSEEK, error_type)

    def get_provider(self) -> LLMProvider:
        """Get the provider type."""
        return LLMProvider.DEEPSEEK

    def get_default_model(self) -> str:
        """Get the default model for Deepseek."""
        return self.DEFAULT_MODEL

    def get_available_models(self) -> List[str]:
        """Get list of available Deepseek models."""
        return self.AVAILABLE_MODELS.copy()

    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
