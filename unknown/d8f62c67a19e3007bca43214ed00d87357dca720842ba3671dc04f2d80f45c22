---
# API System Definitions
# REST API endpoints and routing configuration

api:
  description: "RESTful API endpoints for ho-trans manga translation application"
  intent: "Provide comprehensive API for project management, OCR processing, and translation services"
  base_url: "/api/v1"

  endpoints:
    projects:
      description: "Project management API endpoints"
      intent: "CRUD operations for manga translation projects and pages"
      implementation_status: "COMPLETE"
      implementation_gaps: []
      depends_on:
        - "projects_module"
        - "pagination"
      files:
        - "src/projects/router.py"
      base_path: "/projects"
      endpoints:
        - method: "GET"
          path: "/"
          description: "List projects with pagination"
          response_model: "PaginatedResponse[ProjectResponse]"
          query_params: ["page", "limit"]
        - method: "POST"
          path: "/"
          description: "Create new project"
          request_model: "ProjectCreate"
          response_model: "ProjectResponse"
          status_code: 201
        - method: "GET"
          path: "/{project_id}"
          description: "Get project by ID"
          response_model: "ProjectResponse"
        - method: "PUT"
          path: "/{project_id}"
          description: "Update project"
          request_model: "ProjectUpdate"
          response_model: "ProjectResponse"
        - method: "DELETE"
          path: "/{project_id}"
          description: "Delete project"
          response_model: "SuccessResponse"
        - method: "GET"
          path: "/{project_id}/detail"
          description: "Get project with pages"
          response_model: "ProjectDetailResponse"
        - method: "POST"
          path: "/{project_id}/pages"
          description: "Upload page to project"
          request_model: "ProjectPageCreate"
          response_model: "ProjectPageResponse"
          content_type: "multipart/form-data"
        - method: "GET"
          path: "/{project_id}/pages"
          description: "List project pages"
          response_model: "List[ProjectPageResponse]"
        - method: "GET"
          path: "/{project_id}/pages/{page_id}"
          description: "Get page by ID"
          response_model: "ProjectPageResponse"
        - method: "PUT"
          path: "/{project_id}/pages/{page_id}"
          description: "Update page"
          request_model: "ProjectPageUpdate"
          response_model: "ProjectPageResponse"
        - method: "DELETE"
          path: "/{project_id}/pages/{page_id}"
          description: "Delete page"
          response_model: "SuccessResponse"
        - method: "GET"
          path: "/{project_id}/pages/{page_id}/detail"
          description: "Get page with text regions"
          response_model: "ProjectPageDetailResponse"
        - method: "POST"
          path: "/{project_id}/pages/{page_id}/regions"
          description: "Create text region"
          request_model: "TextRegionCreate"
          response_model: "TextRegionResponse"
        - method: "GET"
          path: "/{project_id}/pages/{page_id}/regions"
          description: "List page text regions"
          response_model: "List[TextRegionResponse]"
        - method: "GET"
          path: "/{project_id}/pages/{page_id}/regions/{region_id}"
          description: "Get text region"
          response_model: "TextRegionResponse"
        - method: "PUT"
          path: "/{project_id}/pages/{page_id}/regions/{region_id}"
          description: "Update text region"
          request_model: "TextRegionUpdate"
          response_model: "TextRegionResponse"
        - method: "DELETE"
          path: "/{project_id}/pages/{page_id}/regions/{region_id}"
          description: "Delete text region"
          response_model: "SuccessResponse"

    ocr:
      description: "OCR processing API endpoints"
      intent: "LLM-based text detection and extraction from manga images"
      implementation_status: "COMPLETE"
      implementation_gaps:
        - "Background job processing"
        - "Job queue management"
      depends_on:
        - "ocr_module"
        - "llm_providers"
      files:
        - "src/ocr/router.py"
      base_path: "/ocr"
      endpoints:
        - method: "POST"
          path: "/jobs"
          description: "Create OCR job"
          request_model: "OCRJobCreate"
          response_model: "OCRJobResponse"
          status_code: 201
        - method: "GET"
          path: "/jobs/{job_id}"
          description: "Get OCR job"
          response_model: "OCRJobResponse"
        - method: "GET"
          path: "/jobs/{job_id}/detail"
          description: "Get OCR job with results"
          response_model: "OCRJobDetailResponse"
        - method: "GET"
          path: "/jobs/{job_id}/results"
          description: "Get OCR results"
          response_model: "List[OCRResultResponse]"
        - method: "GET"
          path: "/pages/{page_id}/jobs"
          description: "Get page OCR jobs"
          response_model: "List[OCRJobResponse]"
        - method: "POST"
          path: "/process"
          description: "Process OCR request"
          request_model: "OCRProcessRequest"
          response_model: "OCRJobResponse"
          status_code: 202
        - method: "POST"
          path: "/process/batch"
          description: "Batch OCR processing"
          request_model: "OCRBatchProcessRequest"
          response_model: "List[OCRJobResponse]"
          status_code: 202
        - method: "GET"
          path: "/statistics"
          description: "Get OCR statistics"
          response_model: "OCRStatistics"
          query_params: ["project_id"]
        - method: "POST"
          path: "/jobs/{job_id}/retry"
          description: "Retry failed OCR job"
          response_model: "OCRJobResponse"

    translation:
      description: "Translation processing API endpoints"
      intent: "LLM-based text translation with alternatives and templates"
      implementation_status: "COMPLETE"
      implementation_gaps:
        - "Translation quality scoring"
        - "Translation memory integration"
        - "Batch translation optimization"
      depends_on:
        - "translation_module"
        - "llm_providers"
        - "pagination"
      files:
        - "src/translation/router.py"
      base_path: "/translation"
      endpoints:
        - method: "POST"
          path: "/jobs"
          description: "Create translation job"
          request_model: "TranslationJobCreate"
          response_model: "TranslationJobResponse"
          status_code: 201
        - method: "GET"
          path: "/jobs/{job_id}"
          description: "Get translation job"
          response_model: "TranslationJobResponse"
        - method: "GET"
          path: "/jobs/{job_id}/detail"
          description: "Get job with alternatives"
          response_model: "TranslationJobDetailResponse"
        - method: "GET"
          path: "/jobs/{job_id}/alternatives"
          description: "Get translation alternatives"
          response_model: "List[TranslationAlternativeResponse]"
        - method: "POST"
          path: "/jobs/{job_id}/alternatives/{alternative_id}/select"
          description: "Select translation alternative"
          response_model: "TranslationJobResponse"
        - method: "GET"
          path: "/regions/{text_region_id}/jobs"
          description: "Get region translation jobs"
          response_model: "List[TranslationJobResponse]"
        - method: "POST"
          path: "/process"
          description: "Process translation request"
          request_model: "TranslationProcessRequest"
          response_model: "TranslationJobResponse"
          status_code: 202
        - method: "POST"
          path: "/process/batch"
          description: "Batch translation processing"
          request_model: "TranslationBatchProcessRequest"
          response_model: "List[TranslationJobResponse]"
          status_code: 202
        - method: "POST"
          path: "/templates"
          description: "Create translation template"
          request_model: "TranslationTemplateCreate"
          response_model: "TranslationTemplateResponse"
          status_code: 201
        - method: "GET"
          path: "/templates"
          description: "List translation templates"
          response_model: "PaginatedResponse[TranslationTemplateResponse]"
          query_params: ["page", "limit", "source_language", "target_language", "category"]
        - method: "GET"
          path: "/templates/{template_id}"
          description: "Get translation template"
          response_model: "TranslationTemplateResponse"
        - method: "PUT"
          path: "/templates/{template_id}"
          description: "Update translation template"
          request_model: "TranslationTemplateUpdate"
          response_model: "TranslationTemplateResponse"
        - method: "DELETE"
          path: "/templates/{template_id}"
          description: "Delete translation template"
          response_model: "SuccessResponse"
        - method: "GET"
          path: "/statistics"
          description: "Get translation statistics"
          response_model: "TranslationStatistics"
          query_params: ["project_id"]

    llm_providers:
      description: "LLM provider integration API endpoints"
      intent: "Direct access to LLM capabilities for text generation, OCR, and translation"
      implementation_status: "COMPLETE"
      implementation_gaps:
        - "Provider health monitoring"
        - "Usage analytics and billing"
        - "Model performance metrics"
      depends_on:
        - "llm_providers_module"
      files:
        - "src/llm_providers/router.py"
      base_path: "/llm"
      endpoints:
        - method: "GET"
          path: "/providers"
          description: "Get providers status"
          response_model: "LLMProvidersStatus"
        - method: "GET"
          path: "/providers/{provider}/models"
          description: "Get provider models"
          response_model: "List[str]"
        - method: "POST"
          path: "/generate/text"
          description: "Generate text"
          request_model: "LLMTextGenerationRequest"
          response_model: "LLMGenerationResponse"
        - method: "POST"
          path: "/generate/batch"
          description: "Batch text generation"
          request_model: "LLMBatchTextRequest"
          response_model: "LLMBatchResponse"
        - method: "POST"
          path: "/analyze/image"
          description: "Analyze image"
          request_model: "LLMImageAnalysisRequest"
          response_model: "LLMGenerationResponse"
          content_type: "multipart/form-data"
        - method: "POST"
          path: "/ocr"
          description: "Perform OCR"
          request_model: "LLMOCRRequest"
          response_model: "LLMOCRResponse"
          content_type: "multipart/form-data"
        - method: "POST"
          path: "/translate"
          description: "Translate text"
          request_model: "LLMTranslationRequest"
          response_model: "LLMTranslationResponse"

  middleware:
    description: "API middleware components"
    intent: "Provide cross-cutting concerns for all API endpoints"
    implementation_status: "PARTIAL"
    implementation_gaps:
      - "Request logging middleware"
      - "Error tracking middleware"
    depends_on:
      - "application"
    files:
      - "src/main.py"
    features:
      - "CORS middleware (implemented)"
      - "Exception handling (implemented)"
      - "Request validation (implemented)"
    planned_features:
      - "Request/response logging"
      - "Metrics collection"
      - "Error tracking integration"

  documentation:
    description: "API documentation and OpenAPI specification"
    intent: "Provide comprehensive API documentation for developers"
    implementation_status: "COMPLETE"
    implementation_gaps: []
    depends_on:
      - "application"
    files:
      - "src/main.py"
    features:
      - "Automatic OpenAPI schema generation"
      - "Swagger UI at /docs"
      - "ReDoc at /redoc"
      - "Comprehensive endpoint descriptions"
      - "Request/response model documentation"
      - "Example requests and responses"

  validation:
    description: "Request and response validation"
    intent: "Ensure data integrity and provide clear error messages"
    implementation_status: "COMPLETE"
    implementation_gaps: []
    depends_on:
      - "schemas"
    features:
      - "Pydantic model validation"
      - "Automatic error responses for invalid data"
      - "Custom validation rules"
      - "Type coercion and conversion"
      - "Detailed validation error messages"
