# Testing Guide

This document provides comprehensive guidance on testing the ho-trans backend application.

## Overview

The testing strategy follows these principles:

- **Async-first**: All tests use proper async/await patterns to prevent event loop issues
- **Isolation**: Each test is independent and doesn't affect others
- **Mocking**: External dependencies are mocked to ensure fast, reliable tests
- **Coverage**: Aim for 80%+ code coverage across all modules

## Test Structure

```
tests/
├── conftest.py              # Global test configuration and fixtures
├── utils.py                 # Test utilities and helpers
├── test_pagination.py       # Pagination system tests
├── projects/
│   └── test_projects_api.py # Project API integration tests
├── ocr/
│   └── test_ocr_api.py      # OCR API integration tests
├── translation/
│   └── test_translation_api.py # Translation API tests
└── llm_providers/
    └── test_llm_api.py      # LLM provider API tests
```

## Running Tests

### Basic Commands

```bash
# Run all tests
make test

# Run with coverage
make test-coverage

# Run unit tests only
make test-unit

# Run integration tests only
make test-integration

# Run tests in parallel
make test-parallel

# Check test environment
make test-check
```

### Module-Specific Tests

```bash
# Test specific modules
make test-projects
make test-ocr
make test-translation
make test-llm
make test-pagination

# Or using pytest directly
pytest tests/projects/ -v
pytest tests/ocr/ -v
```

### Advanced Test Running

```bash
# Run specific test file
pytest tests/test_pagination.py -v

# Run specific test function
pytest tests/test_pagination.py::TestPaginationParams::test_pagination_params_defaults -v

# Run tests with markers
pytest tests/ -v -m "unit"
pytest tests/ -v -m "integration"
pytest tests/ -v -m "not slow"

# Run tests with keyword matching
pytest tests/ -v -k "pagination"
```

## Test Configuration

### Async Test Setup

The test configuration prevents event loop issues by using:

```python
# pytest.ini
[tool:pytest]
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function
```

### Test Client

All API tests use the async test client:

```python
@pytest.fixture
async def client() -> AsyncGenerator[AsyncClient, None]:
    """Create an async test client for the FastAPI application."""
    app = create_app()
    host, port = "127.0.0.1", "9000"
    
    async with AsyncClient(
        transport=ASGITransport(app=app, client=(host, port)), 
        base_url="http://test"
    ) as client:
        yield client
```

### Database Testing

Two approaches for database testing:

1. **Mock Database** (faster, for unit tests):
```python
@pytest.fixture
async def mock_database() -> AsyncMock:
    """Create a mock database for testing."""
    mock_db = AsyncMock(spec=Database)
    return mock_db
```

2. **In-Memory Database** (for integration tests):
```python
@pytest.fixture
async def test_database() -> AsyncGenerator[Database, None]:
    """Create a test database instance."""
    test_db_url = "sqlite:///:memory:"
    database = Database(test_db_url)
    
    try:
        await database.connect()
        yield database
    finally:
        await database.disconnect()
```

## Writing Tests

### API Integration Tests

```python
@pytest.mark.asyncio
async def test_create_project(client: AsyncClient, sample_project_data):
    """Test creating a new project."""
    with patch('src.projects.service.ProjectService.create_project') as mock_create:
        mock_create.return_value = {
            "id": "test-project-id",
            "name": sample_project_data["name"],
            # ... other fields
        }
        
        response = await client.post("/api/v1/projects", json=sample_project_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == sample_project_data["name"]
```

### Service Layer Tests

```python
@pytest.mark.asyncio
async def test_paginator_basic():
    """Test basic pagination functionality."""
    mock_db = AsyncMock()
    mock_query = MagicMock()
    
    # Mock database responses
    mock_db.fetch_val.return_value = 25  # Total count
    mock_db.fetch_all.return_value = [
        {"id": 1, "name": "item1"},
        {"id": 2, "name": "item2"}
    ]
    
    paginator = Paginator(mock_db)
    params = PaginationParams(page=1, limit=10)
    
    items, total = await paginator.paginate(mock_query, params)
    
    assert total == 25
    assert len(items) == 2
```

### Error Handling Tests

```python
@pytest.mark.asyncio
async def test_project_not_found(client: AsyncClient):
    """Test handling of non-existent project."""
    project_id = "non-existent-id"
    
    with patch('src.projects.service.ProjectService.get_project') as mock_get:
        from src.projects.exceptions import ProjectNotFound
        mock_get.side_effect = ProjectNotFound("Project not found")
        
        response = await client.get(f"/api/v1/projects/{project_id}")
        
        assert response.status_code == 404
```

## Test Fixtures

### Global Fixtures (conftest.py)

- `client`: Async HTTP client for API testing
- `mock_database`: Mock database for unit tests
- `test_database`: In-memory database for integration tests
- `sample_*_data`: Sample data for various entities

### Using Fixtures

```python
async def test_with_fixtures(
    client: AsyncClient,
    sample_project_data,
    mock_database
):
    """Test using multiple fixtures."""
    # Test implementation
    pass
```

## Mocking Strategies

### Service Layer Mocking

```python
with patch('src.projects.service.ProjectService.create_project') as mock_create:
    mock_create.return_value = expected_response
    # Test code
```

### LLM Provider Mocking

```python
with patch('src.llm_providers.service.llm_service.generate_text') as mock_generate:
    mock_response = create_mock_llm_response("Hello")
    mock_generate.return_value = mock_response
    # Test code
```

### Database Mocking

```python
mock_db = AsyncMock()
mock_db.fetch_all.return_value = [{"id": 1, "name": "test"}]
mock_db.fetch_val.return_value = 10
```

## Test Markers

Use markers to categorize tests:

```python
@pytest.mark.unit
async def test_unit_function():
    """Unit test example."""
    pass

@pytest.mark.integration
async def test_api_endpoint(client: AsyncClient):
    """Integration test example."""
    pass

@pytest.mark.slow
async def test_heavy_operation():
    """Slow test example."""
    pass

@pytest.mark.external
async def test_external_service():
    """Test requiring external services."""
    pass
```

## Coverage Requirements

- **Minimum Coverage**: 80%
- **Critical Paths**: 95%+ (authentication, data validation, error handling)
- **Exclusions**: Migration files, configuration files

### Checking Coverage

```bash
# Generate coverage report
make test-coverage

# View HTML report
open htmlcov/index.html

# Check coverage for specific module
pytest tests/projects/ --cov=src.projects --cov-report=term
```

## Best Practices

### 1. Test Naming

```python
# Good
async def test_create_project_with_valid_data():
    pass

async def test_create_project_returns_404_when_not_found():
    pass

# Avoid
async def test_project():
    pass
```

### 2. Test Structure (AAA Pattern)

```python
async def test_example():
    # Arrange
    project_data = {"name": "Test Project"}
    
    # Act
    response = await client.post("/api/v1/projects", json=project_data)
    
    # Assert
    assert response.status_code == 201
    assert response.json()["name"] == "Test Project"
```

### 3. Async/Await Usage

```python
# Always mark async tests
@pytest.mark.asyncio
async def test_async_function():
    result = await some_async_function()
    assert result is not None
```

### 4. Mock Cleanup

```python
# Use context managers for mocking
with patch('module.function') as mock_func:
    mock_func.return_value = "test"
    # Test code

# Or use decorators for class-level mocking
@patch('module.function')
async def test_with_mock(mock_func):
    mock_func.return_value = "test"
    # Test code
```

### 5. Error Testing

```python
# Test both success and failure cases
async def test_success_case():
    # Test successful operation
    pass

async def test_failure_case():
    # Test error handling
    pass
```

## Debugging Tests

### Running Single Tests

```bash
# Run with verbose output
pytest tests/test_pagination.py::test_specific_function -v -s

# Run with debugging
pytest tests/test_pagination.py::test_specific_function -v -s --pdb
```

### Common Issues

1. **Event Loop Errors**:
   - Ensure `@pytest.mark.asyncio` is used
   - Check `asyncio_mode = auto` in configuration

2. **Mock Issues**:
   - Verify mock paths are correct
   - Use `spec=` parameter for type safety

3. **Database Issues**:
   - Ensure proper cleanup in fixtures
   - Use transactions for isolation

## Continuous Integration

Tests run automatically on:

- Push to main/develop branches
- Pull requests
- Multiple Python versions (3.11, 3.12)

### CI Pipeline

1. **Setup**: Install dependencies, cache pip
2. **Linting**: ruff, black, isort
3. **Type Checking**: mypy
4. **Unit Tests**: Fast tests with mocks
5. **Integration Tests**: API endpoint tests
6. **Database Tests**: Migration and initialization
7. **Security**: safety and bandit checks
8. **Coverage**: Upload to Codecov

## Performance Testing

For performance-critical code:

```python
@pytest.mark.slow
async def test_pagination_performance():
    """Test pagination with large datasets."""
    # Performance test implementation
    pass
```

Run performance tests separately:
```bash
pytest tests/ -v -m slow
```
