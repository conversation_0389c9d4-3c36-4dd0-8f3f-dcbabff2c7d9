/**
 * Centralized API client for ho-trans backend
 */

import {
  ProjectCreate,
  ProjectUpdate,
  ProjectResponse,
  ProjectDetailResponse,
  ProjectPageResponse,
  ProjectPageDetailResponse,
  TextRegionCreate,
  TextRegionUpdate,
  TextRegionResponse,
  OCRJobCreate,
  OCRJobResponse,
  OCRJobDetailResponse,
  OCRProcessRequest,
  OCRBatchProcessRequest,
  OCRStatistics,
  TranslationJobCreate,
  TranslationJobResponse,
  TranslationJobDetailResponse,
  TranslationProcessRequest,
  TranslationBatchProcessRequest,
  TranslationStatistics,
  LLMProvidersStatus,
  PaginatedResponse,
  PaginationParams,
  ErrorResponse,
  SuccessResponse
} from '@/types/api';

// API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
const API_VERSION = 'v1';

// Request configuration
interface RequestConfig {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  signal?: AbortSignal;
}

// API Error class
export class APIError extends Error {
  constructor(
    public status: number,
    public errorCode: string,
    public detail: string,
    public timestamp: string
  ) {
    super(`API Error ${status}: ${detail}`);
    this.name = 'APIError';
  }
}

// Base API client class
class BaseAPIClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(endpoint: string, config: RequestConfig): Promise<T> {
    const url = `${this.baseUrl}/api/${API_VERSION}${endpoint}`;

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...config.headers
    };

    const requestInit: RequestInit = {
      method: config.method,
      headers,
      signal: config.signal
    };

    if (config.body && config.method !== 'GET') {
      if (config.body instanceof FormData) {
        // Remove Content-Type for FormData to let browser set it with boundary
        delete headers['Content-Type'];
        requestInit.body = config.body;
      } else {
        requestInit.body = JSON.stringify(config.body);
      }
    }

    try {
      const response = await fetch(url, requestInit);

      if (!response.ok) {
        let errorData: ErrorResponse;
        try {
          errorData = await response.json();
        } catch {
          // Fallback for non-JSON error responses
          errorData = {
            error_code: 'UNKNOWN_ERROR',
            detail: `HTTP ${response.status}: ${response.statusText}`,
            timestamp: new Date().toISOString()
          };
        }

        throw new APIError(
          response.status,
          errorData.error_code,
          errorData.detail,
          errorData.timestamp
        );
      }

      // Handle empty responses (204 No Content)
      if (response.status === 204) {
        return {} as T;
      }

      return await response.json();
    } catch (error) {
      if (error instanceof APIError) {
        throw error;
      }

      // Handle network errors, timeouts, etc.
      throw new APIError(
        0,
        'NETWORK_ERROR',
        error instanceof Error ? error.message : 'Unknown network error',
        new Date().toISOString()
      );
    }
  }

  // HTTP method helpers
  protected get<T>(endpoint: string, signal?: AbortSignal): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET', signal });
  }

  protected post<T>(endpoint: string, body?: any, signal?: AbortSignal): Promise<T> {
    return this.request<T>(endpoint, { method: 'POST', body, signal });
  }

  protected put<T>(endpoint: string, body?: any, signal?: AbortSignal): Promise<T> {
    return this.request<T>(endpoint, { method: 'PUT', body, signal });
  }

  protected delete<T>(endpoint: string, signal?: AbortSignal): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE', signal });
  }

  protected upload<T>(endpoint: string, formData: FormData, signal?: AbortSignal): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: formData,
      signal,
      headers: {} // Let browser set Content-Type for FormData
    });
  }
}

// Projects API client
export class ProjectsAPI extends BaseAPIClient {
  // Project CRUD operations
  async createProject(data: ProjectCreate, signal?: AbortSignal): Promise<ProjectResponse> {
    return this.post<ProjectResponse>('/projects', data, signal);
  }

  async getProjects(params?: PaginationParams, signal?: AbortSignal): Promise<PaginatedResponse<ProjectResponse>> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.set('page', params.page.toString());
    if (params?.limit) queryParams.set('limit', params.limit.toString());

    const endpoint = `/projects${queryParams.toString() ? `?${queryParams}` : ''}`;
    return this.get<PaginatedResponse<ProjectResponse>>(endpoint, signal);
  }

  async getProject(projectId: string, signal?: AbortSignal): Promise<ProjectResponse> {
    return this.get<ProjectResponse>(`/projects/${projectId}`, signal);
  }

  async getProjectDetail(projectId: string, signal?: AbortSignal): Promise<ProjectDetailResponse> {
    return this.get<ProjectDetailResponse>(`/projects/${projectId}/detail`, signal);
  }

  async updateProject(projectId: string, data: ProjectUpdate, signal?: AbortSignal): Promise<ProjectResponse> {
    return this.put<ProjectResponse>(`/projects/${projectId}`, data, signal);
  }

  async deleteProject(projectId: string, signal?: AbortSignal): Promise<SuccessResponse> {
    return this.delete<SuccessResponse>(`/projects/${projectId}`, signal);
  }

  // Project Pages operations
  async uploadPage(
    projectId: string,
    file: File,
    pageNumber: number,
    signal?: AbortSignal
  ): Promise<ProjectPageResponse> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('page_number', pageNumber.toString());

    return this.upload<ProjectPageResponse>(`/projects/${projectId}/pages`, formData, signal);
  }

  async getProjectPages(
    projectId: string,
    params?: PaginationParams,
    signal?: AbortSignal
  ): Promise<PaginatedResponse<ProjectPageResponse>> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.set('page', params.page.toString());
    if (params?.limit) queryParams.set('limit', params.limit.toString());

    const endpoint = `/projects/${projectId}/pages${queryParams.toString() ? `?${queryParams}` : ''}`;
    return this.get<PaginatedResponse<ProjectPageResponse>>(endpoint, signal);
  }

  async getProjectPage(
    projectId: string,
    pageId: string,
    signal?: AbortSignal
  ): Promise<ProjectPageResponse> {
    return this.get<ProjectPageResponse>(`/projects/${projectId}/pages/${pageId}`, signal);
  }

  async getProjectPageDetail(
    projectId: string,
    pageId: string,
    signal?: AbortSignal
  ): Promise<ProjectPageDetailResponse> {
    return this.get<ProjectPageDetailResponse>(`/projects/${projectId}/pages/${pageId}/detail`, signal);
  }

  async deleteProjectPage(
    projectId: string,
    pageId: string,
    signal?: AbortSignal
  ): Promise<SuccessResponse> {
    return this.delete<SuccessResponse>(`/projects/${projectId}/pages/${pageId}`, signal);
  }

  // Text Regions operations
  async createTextRegion(
    projectId: string,
    pageId: string,
    data: TextRegionCreate,
    signal?: AbortSignal
  ): Promise<TextRegionResponse> {
    return this.post<TextRegionResponse>(`/projects/${projectId}/pages/${pageId}/regions`, data, signal);
  }

  async getTextRegions(
    projectId: string,
    pageId: string,
    signal?: AbortSignal
  ): Promise<TextRegionResponse[]> {
    return this.get<TextRegionResponse[]>(`/projects/${projectId}/pages/${pageId}/regions`, signal);
  }

  async getTextRegion(
    projectId: string,
    pageId: string,
    regionId: string,
    signal?: AbortSignal
  ): Promise<TextRegionResponse> {
    return this.get<TextRegionResponse>(`/projects/${projectId}/pages/${pageId}/regions/${regionId}`, signal);
  }

  async updateTextRegion(
    projectId: string,
    pageId: string,
    regionId: string,
    data: TextRegionUpdate,
    signal?: AbortSignal
  ): Promise<TextRegionResponse> {
    return this.put<TextRegionResponse>(`/projects/${projectId}/pages/${pageId}/regions/${regionId}`, data, signal);
  }

  async deleteTextRegion(
    projectId: string,
    pageId: string,
    regionId: string,
    signal?: AbortSignal
  ): Promise<SuccessResponse> {
    return this.delete<SuccessResponse>(`/projects/${projectId}/pages/${pageId}/regions/${regionId}`, signal);
  }
}

// OCR API client
export class OCRAPI extends BaseAPIClient {
  // OCR Job operations
  async createOCRJob(data: OCRJobCreate, signal?: AbortSignal): Promise<OCRJobResponse> {
    return this.post<OCRJobResponse>('/ocr/jobs', data, signal);
  }

  async getOCRJob(jobId: string, signal?: AbortSignal): Promise<OCRJobResponse> {
    return this.get<OCRJobResponse>(`/ocr/jobs/${jobId}`, signal);
  }

  async getOCRJobDetail(jobId: string, signal?: AbortSignal): Promise<OCRJobDetailResponse> {
    return this.get<OCRJobDetailResponse>(`/ocr/jobs/${jobId}/detail`, signal);
  }

  async getPageOCRJobs(pageId: string, signal?: AbortSignal): Promise<OCRJobResponse[]> {
    return this.get<OCRJobResponse[]>(`/ocr/pages/${pageId}/jobs`, signal);
  }

  async getOCRJobs(projectId?: string, params?: PaginationParams, signal?: AbortSignal): Promise<PaginatedResponse<OCRJobResponse>> {
    const queryParams = new URLSearchParams();
    if (projectId) queryParams.append('project_id', projectId);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const url = `/ocr/jobs${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.get<PaginatedResponse<OCRJobResponse>>(url, signal);
  }

  // OCR Processing operations
  async processOCR(request: OCRProcessRequest, signal?: AbortSignal): Promise<OCRJobResponse> {
    return this.post<OCRJobResponse>('/ocr/process', request, signal);
  }

  async processBatchOCR(request: OCRBatchProcessRequest, signal?: AbortSignal): Promise<OCRJobResponse[]> {
    return this.post<OCRJobResponse[]>('/ocr/process/batch', request, signal);
  }

  async retryOCRJob(jobId: string, signal?: AbortSignal): Promise<OCRJobResponse> {
    return this.post<OCRJobResponse>(`/ocr/jobs/${jobId}/retry`, {}, signal);
  }

  // Statistics
  async getOCRStatistics(signal?: AbortSignal): Promise<OCRStatistics> {
    return this.get<OCRStatistics>('/ocr/statistics', signal);
  }
}

// Translation API client
export class TranslationAPI extends BaseAPIClient {
  // Translation Job operations
  async createTranslationJob(data: TranslationJobCreate, signal?: AbortSignal): Promise<TranslationJobResponse> {
    return this.post<TranslationJobResponse>('/translation/jobs', data, signal);
  }

  async getTranslationJob(jobId: string, signal?: AbortSignal): Promise<TranslationJobResponse> {
    return this.get<TranslationJobResponse>(`/translation/jobs/${jobId}`, signal);
  }

  async getTranslationJobDetail(jobId: string, signal?: AbortSignal): Promise<TranslationJobDetailResponse> {
    return this.get<TranslationJobDetailResponse>(`/translation/jobs/${jobId}/detail`, signal);
  }

  async getRegionTranslationJobs(regionId: string, signal?: AbortSignal): Promise<TranslationJobResponse[]> {
    return this.get<TranslationJobResponse[]>(`/translation/regions/${regionId}/jobs`, signal);
  }

  async getTranslationJobs(projectId?: string, params?: PaginationParams, signal?: AbortSignal): Promise<PaginatedResponse<TranslationJobResponse>> {
    const queryParams = new URLSearchParams();
    if (projectId) queryParams.append('project_id', projectId);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const url = `/translation/jobs${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.get<PaginatedResponse<TranslationJobResponse>>(url, signal);
  }

  // Translation Processing operations
  async processTranslation(request: TranslationProcessRequest, signal?: AbortSignal): Promise<TranslationJobResponse> {
    return this.post<TranslationJobResponse>('/translation/process', request, signal);
  }

  async processBatchTranslation(request: TranslationBatchProcessRequest, signal?: AbortSignal): Promise<TranslationJobResponse[]> {
    return this.post<TranslationJobResponse[]>('/translation/process/batch', request, signal);
  }

  async retryTranslationJob(jobId: string, signal?: AbortSignal): Promise<TranslationJobResponse> {
    return this.post<TranslationJobResponse>(`/translation/jobs/${jobId}/retry`, {}, signal);
  }

  // Statistics
  async getTranslationStatistics(signal?: AbortSignal): Promise<TranslationStatistics> {
    return this.get<TranslationStatistics>('/translation/statistics', signal);
  }
}

// LLM Providers API client
export class LLMProvidersAPI extends BaseAPIClient {
  // Provider information
  async getProvidersStatus(signal?: AbortSignal): Promise<LLMProvidersStatus> {
    return this.get<LLMProvidersStatus>('/llm/providers', signal);
  }

  async getProviderModels(provider: string, signal?: AbortSignal): Promise<string[]> {
    return this.get<string[]>(`/llm/providers/${provider}/models`, signal);
  }
}

// Main API client that combines all sub-clients
export class APIClient {
  public projects: ProjectsAPI;
  public ocr: OCRAPI;
  public translation: TranslationAPI;
  public llmProviders: LLMProvidersAPI;

  constructor(baseUrl?: string) {
    this.projects = new ProjectsAPI(baseUrl);
    this.ocr = new OCRAPI(baseUrl);
    this.translation = new TranslationAPI(baseUrl);
    this.llmProviders = new LLMProvidersAPI(baseUrl);
  }

  // Health check
  async healthCheck(signal?: AbortSignal): Promise<{ status: string; version: string }> {
    const url = `${API_BASE_URL}/health`;
    const response = await fetch(url, { signal });

    if (!response.ok) {
      throw new APIError(
        response.status,
        'HEALTH_CHECK_FAILED',
        `Health check failed: ${response.statusText}`,
        new Date().toISOString()
      );
    }

    return response.json();
  }
}

// Default API client instance
export const apiClient = new APIClient();

// Export individual clients for specific use cases
export const projectsAPI = apiClient.projects;
export const ocrAPI = apiClient.ocr;
export const translationAPI = apiClient.translation;
export const llmProvidersAPI = apiClient.llmProviders;
