"""
Service layer for LLM provider operations.
"""
import json
from typing import List, Optional, Dict, Any, Union
from pathlib import Path

from src.constants import LLMProvider, DEFAULT_OCR_PROMPT, DEFAULT_TRANSLATION_PROMPT
from src.llm_providers.base import Base<PERSON><PERSON>lient, LLMMessage, LLMResponse
from src.llm_providers.factory import llm_client_manager
from src.ocr.schemas import LLMOCRResponse, LLMOCRRegion
from src.translation.schemas import LLMTranslationResponse


class LLMService:
    """Service for LLM operations including OCR and translation."""
    
    def __init__(self):
        self.client_manager = llm_client_manager
    
    async def perform_ocr(
        self,
        image_data: bytes,
        image_format: str,
        provider: Optional[LLMProvider] = None,
        custom_prompt: Optional[str] = None,
        model: Optional[str] = None
    ) -> LLMOCRResponse:
        """Perform OCR on an image using LLM."""
        client = self.client_manager.get_client(provider)
        prompt = custom_prompt or DEFAULT_OCR_PROMPT
        
        messages = [
            LLMMessage(role="system", content="You are an expert at reading text from manga/comic images."),
            LLMMessage(role="user", content=prompt)
        ]
        
        try:
            response = await client.generate_text_with_image(
                messages=messages,
                image_data=image_data,
                image_format=image_format,
                model=model,
                temperature=0.3  # Lower temperature for more consistent OCR
            )
            
            # Parse the response as JSON
            try:
                parsed_response = json.loads(response.content)
                return self._parse_ocr_response(parsed_response)
            except json.JSONDecodeError:
                # If JSON parsing fails, try to extract text regions from plain text
                return self._parse_ocr_text_response(response.content)
                
        except Exception as e:
            # Return empty response on error
            return LLMOCRResponse(regions=[], metadata={"error": str(e)})
    
    async def perform_translation(
        self,
        text: str,
        source_language: str,
        target_language: str,
        text_type: str = "unknown",
        provider: Optional[LLMProvider] = None,
        custom_prompt: Optional[str] = None,
        model: Optional[str] = None,
        generate_alternatives: bool = False
    ) -> LLMTranslationResponse:
        """Perform translation using LLM."""
        client = self.client_manager.get_client(provider)
        
        if custom_prompt:
            prompt = custom_prompt
        else:
            prompt = DEFAULT_TRANSLATION_PROMPT.format(
                source_language=source_language,
                target_language=target_language,
                text=text,
                text_type=text_type
            )
        
        if generate_alternatives:
            prompt += "\n\nPlease provide 2-3 alternative translations as well."
        
        messages = [
            LLMMessage(role="system", content=f"You are an expert translator specializing in {source_language} to {target_language} translation."),
            LLMMessage(role="user", content=prompt)
        ]
        
        try:
            response = await client.generate_text(
                messages=messages,
                model=model,
                temperature=0.7
            )
            
            # Try to parse as JSON first, fall back to plain text
            try:
                parsed_response = json.loads(response.content)
                return self._parse_translation_response(parsed_response)
            except json.JSONDecodeError:
                return LLMTranslationResponse(
                    translated_text=response.content.strip(),
                    confidence=0.8,
                    alternatives=[],
                    metadata={"provider": response.provider.value, "model": response.model}
                )
                
        except Exception as e:
            return LLMTranslationResponse(
                translated_text="",
                confidence=0.0,
                alternatives=[],
                metadata={"error": str(e)}
            )
    
    async def generate_text(
        self,
        prompt: str,
        provider: Optional[LLMProvider] = None,
        model: Optional[str] = None,
        system_message: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None
    ) -> LLMResponse:
        """Generate text using LLM."""
        client = self.client_manager.get_client(provider)
        
        messages = []
        if system_message:
            messages.append(LLMMessage(role="system", content=system_message))
        messages.append(LLMMessage(role="user", content=prompt))
        
        return await client.generate_text(
            messages=messages,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens
        )
    
    async def analyze_image(
        self,
        image_data: bytes,
        image_format: str,
        prompt: str,
        provider: Optional[LLMProvider] = None,
        model: Optional[str] = None,
        system_message: Optional[str] = None
    ) -> LLMResponse:
        """Analyze an image using LLM."""
        client = self.client_manager.get_client(provider)
        
        messages = []
        if system_message:
            messages.append(LLMMessage(role="system", content=system_message))
        messages.append(LLMMessage(role="user", content=prompt))
        
        return await client.generate_text_with_image(
            messages=messages,
            image_data=image_data,
            image_format=image_format,
            model=model
        )
    
    def _parse_ocr_response(self, response_data: Dict[str, Any]) -> LLMOCRResponse:
        """Parse structured OCR response from LLM."""
        regions = []
        
        if "regions" in response_data:
            for region_data in response_data["regions"]:
                try:
                    region = LLMOCRRegion(
                        text=region_data.get("text", ""),
                        type=region_data.get("type", "other"),
                        confidence=region_data.get("confidence"),
                        coordinates=region_data.get("coordinates")
                    )
                    regions.append(region)
                except Exception:
                    # Skip invalid regions
                    continue
        
        return LLMOCRResponse(
            regions=regions,
            metadata=response_data.get("metadata", {})
        )
    
    def _parse_ocr_text_response(self, response_text: str) -> LLMOCRResponse:
        """Parse plain text OCR response from LLM."""
        # Simple parsing for plain text responses
        # This is a fallback when JSON parsing fails
        lines = response_text.strip().split('\n')
        regions = []
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#') and not line.startswith('//'):
                region = LLMOCRRegion(
                    text=line,
                    type="other",
                    confidence=0.7
                )
                regions.append(region)
        
        return LLMOCRResponse(
            regions=regions,
            metadata={"parsed_from": "plain_text"}
        )
    
    def _parse_translation_response(self, response_data: Dict[str, Any]) -> LLMTranslationResponse:
        """Parse structured translation response from LLM."""
        return LLMTranslationResponse(
            translated_text=response_data.get("translated_text", ""),
            confidence=response_data.get("confidence"),
            alternatives=response_data.get("alternatives", []),
            metadata=response_data.get("metadata", {})
        )
    
    def get_available_providers(self) -> List[LLMProvider]:
        """Get list of available LLM providers."""
        return self.client_manager.get_available_providers()
    
    def is_provider_available(self, provider: LLMProvider) -> bool:
        """Check if a provider is available."""
        return self.client_manager.is_provider_available(provider)
    
    async def close(self):
        """Close all LLM client connections."""
        await self.client_manager.close_all()


# Global LLM service instance
llm_service = LLMService()
