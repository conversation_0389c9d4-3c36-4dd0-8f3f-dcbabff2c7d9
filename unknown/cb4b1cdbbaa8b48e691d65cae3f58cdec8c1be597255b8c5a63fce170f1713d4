"""
Global Pydantic schemas and base classes.
"""
from datetime import datetime
from typing import Any, Dict
from zoneinfo import ZoneInfo

from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel, ConfigDict, Field
from src.utils import utcnow


def datetime_to_gmt_str(dt: datetime) -> str:
    """Convert datetime to GMT string format."""
    if not dt.tzinfo:
        dt = dt.replace(tzinfo=ZoneInfo("UTC"))
    return dt.strftime("%Y-%m-%dT%H:%M:%S%z")


class CustomModel(BaseModel):
    """Custom base model with global configurations."""

    model_config = ConfigDict(
        json_encoders={datetime: datetime_to_gmt_str},
        populate_by_name=True,
        from_attributes=True,  # Enable ORM mode for SQLAlchemy models
        str_strip_whitespace=True,
        validate_assignment=True,
    )

    def serializable_dict(self, **kwargs) -> Dict[str, Any]:
        """Return a dict which contains only serializable fields."""
        default_dict = self.model_dump()
        return jsonable_encoder(default_dict)


class BaseSchema(CustomModel):
    """Base schema with common fields."""

    id: str = Field(..., description="Unique identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")


# Pagination classes moved to src.pagination module


class ErrorResponse(CustomModel):
    """Error response schema."""

    error_code: str = Field(..., description="Error code")
    detail: str = Field(..., description="Error detail message")
    timestamp: datetime = Field(
        default_factory=utcnow, description="Error timestamp")


class SuccessResponse(CustomModel):
    """Success response schema."""

    message: str = Field(..., description="Success message")
    data: Any = Field(default=None, description="Response data")
