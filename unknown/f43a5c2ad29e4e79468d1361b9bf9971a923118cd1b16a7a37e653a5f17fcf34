"""
Dependencies for translation processing endpoints.
"""
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from src.database import get_db
from src.translation.service import TranslationService
from src.translation.schemas import TranslationJobResponse, TranslationTemplateResponse


async def get_translation_service(session: AsyncSession = Depends(get_db)) -> TranslationService:
    """Dependency to get translation service instance."""
    return TranslationService(session)


async def valid_translation_job_id(
    job_id: str,
    service: TranslationService = Depends(get_translation_service)
) -> TranslationJobResponse:
    """Dependency to validate translation job ID and return job data."""
    return await service.get_translation_job(job_id)


async def valid_translation_template_id(
    template_id: str,
    service: TranslationService = Depends(get_translation_service)
) -> TranslationTemplateResponse:
    """Dependency to validate translation template ID and return template data."""
    return await service.get_translation_template(template_id)
