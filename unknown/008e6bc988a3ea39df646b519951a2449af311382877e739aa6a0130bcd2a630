---
# Database System Definitions
# Database schema, models, and data management

database:
  description: "SQLite database with SQLAlchemy ORM for manga translation data"
  intent: "Provide persistent storage for projects, OCR results, translations, and metadata"
  engine: "SQLite"
  orm: "SQLAlchemy 2.0"
  migration_tool: "Alembic"
  
  configuration:
    description: "Database connection and configuration management"
    intent: "Manage database connections with proper async support"
    implementation_status: "COMPLETE"
    implementation_gaps: []
    depends_on: []
    files:
      - "src/database.py"
      - "src/config.py"
    features:
      - "Async database connection using databases library"
      - "Environment-based database URL configuration"
      - "Connection pooling and management"
      - "Database dependency injection for FastAPI"
      - "Graceful connection handling in application lifecycle"

  schema:
    description: "Database schema design and relationships"
    intent: "Structured data model supporting manga translation workflow"
    implementation_status: "COMPLETE"
    implementation_gaps: []
    depends_on: []
    
    tables:
      project:
        description: "Main project entity for manga translation projects"
        intent: "Store project metadata and configuration"
        implementation_status: "COMPLETE"
        columns:
          - name: "id"
            type: "String(36)"
            constraints: ["PRIMARY KEY"]
            description: "UUID primary key"
          - name: "created_at"
            type: "DateTime"
            constraints: ["NOT NULL"]
            description: "Creation timestamp"
          - name: "updated_at"
            type: "DateTime"
            constraints: ["NOT NULL"]
            description: "Last update timestamp"
          - name: "name"
            type: "String(255)"
            constraints: ["NOT NULL"]
            description: "Project name"
          - name: "description"
            type: "Text"
            constraints: []
            description: "Project description"
          - name: "status"
            type: "String(50)"
            constraints: ["NOT NULL"]
            description: "Project status (draft, in_progress, completed, archived)"
          - name: "source_language"
            type: "String(50)"
            constraints: ["NOT NULL"]
            description: "Source language code"
          - name: "target_language"
            type: "String(50)"
            constraints: ["NOT NULL"]
            description: "Target language code"
        indexes:
          - name: "project_name_idx"
            columns: ["name"]
            type: "btree"
        relationships:
          - target: "project_page"
            type: "one_to_many"
            foreign_key: "project_id"

      project_page:
        description: "Individual manga pages within projects"
        intent: "Store page metadata and file information"
        implementation_status: "COMPLETE"
        columns:
          - name: "id"
            type: "String(36)"
            constraints: ["PRIMARY KEY"]
            description: "UUID primary key"
          - name: "created_at"
            type: "DateTime"
            constraints: ["NOT NULL"]
            description: "Creation timestamp"
          - name: "updated_at"
            type: "DateTime"
            constraints: ["NOT NULL"]
            description: "Last update timestamp"
          - name: "project_id"
            type: "String(36)"
            constraints: ["NOT NULL", "FOREIGN KEY"]
            description: "Reference to project"
          - name: "page_number"
            type: "Integer"
            constraints: ["NOT NULL"]
            description: "Page number within project"
          - name: "original_filename"
            type: "String(255)"
            constraints: ["NOT NULL"]
            description: "Original uploaded filename"
          - name: "file_path"
            type: "String(500)"
            constraints: ["NOT NULL"]
            description: "File storage path"
          - name: "file_size"
            type: "Integer"
            constraints: ["NOT NULL"]
            description: "File size in bytes"
          - name: "image_width"
            type: "Integer"
            constraints: []
            description: "Image width in pixels"
          - name: "image_height"
            type: "Integer"
            constraints: []
            description: "Image height in pixels"
          - name: "ocr_status"
            type: "String(50)"
            constraints: ["NOT NULL"]
            description: "OCR processing status"
        relationships:
          - target: "project"
            type: "many_to_one"
            foreign_key: "project_id"
          - target: "text_region"
            type: "one_to_many"
            foreign_key: "page_id"
          - target: "ocr_job"
            type: "one_to_many"
            foreign_key: "page_id"

      text_region:
        description: "Text regions identified within manga pages"
        intent: "Store text region coordinates, content, and translation status"
        implementation_status: "COMPLETE"
        columns:
          - name: "id"
            type: "String(36)"
            constraints: ["PRIMARY KEY"]
            description: "UUID primary key"
          - name: "created_at"
            type: "DateTime"
            constraints: ["NOT NULL"]
            description: "Creation timestamp"
          - name: "updated_at"
            type: "DateTime"
            constraints: ["NOT NULL"]
            description: "Last update timestamp"
          - name: "page_id"
            type: "String(36)"
            constraints: ["NOT NULL", "FOREIGN KEY"]
            description: "Reference to project page"
          - name: "region_type"
            type: "String(50)"
            constraints: ["NOT NULL"]
            description: "Type of text region (speech_bubble, narration, etc.)"
          - name: "x"
            type: "Float"
            constraints: ["NOT NULL"]
            description: "X coordinate (normalized 0-1)"
          - name: "y"
            type: "Float"
            constraints: ["NOT NULL"]
            description: "Y coordinate (normalized 0-1)"
          - name: "width"
            type: "Float"
            constraints: ["NOT NULL"]
            description: "Width (normalized 0-1)"
          - name: "height"
            type: "Float"
            constraints: ["NOT NULL"]
            description: "Height (normalized 0-1)"
          - name: "original_text"
            type: "Text"
            constraints: []
            description: "Original detected text"
          - name: "confidence_score"
            type: "Float"
            constraints: []
            description: "OCR confidence score"
          - name: "translated_text"
            type: "Text"
            constraints: []
            description: "Translated text"
          - name: "translation_status"
            type: "String(50)"
            constraints: ["NOT NULL"]
            description: "Translation status"
          - name: "font_family"
            type: "String(100)"
            constraints: []
            description: "Font family for rendering"
          - name: "font_size"
            type: "Integer"
            constraints: []
            description: "Font size for rendering"
          - name: "font_color"
            type: "String(7)"
            constraints: []
            description: "Font color (hex)"
          - name: "background_color"
            type: "String(7)"
            constraints: []
            description: "Background color (hex)"
        relationships:
          - target: "project_page"
            type: "many_to_one"
            foreign_key: "page_id"
          - target: "translation_job"
            type: "one_to_many"
            foreign_key: "text_region_id"

      ocr_job:
        description: "OCR processing jobs and their status"
        intent: "Track OCR processing requests and results"
        implementation_status: "COMPLETE"
        columns:
          - name: "id"
            type: "String(36)"
            constraints: ["PRIMARY KEY"]
            description: "UUID primary key"
          - name: "created_at"
            type: "DateTime"
            constraints: ["NOT NULL"]
            description: "Creation timestamp"
          - name: "updated_at"
            type: "DateTime"
            constraints: ["NOT NULL"]
            description: "Last update timestamp"
          - name: "page_id"
            type: "String(36)"
            constraints: ["NOT NULL", "FOREIGN KEY"]
            description: "Reference to project page"
          - name: "status"
            type: "String(50)"
            constraints: ["NOT NULL"]
            description: "Job status (pending, processing, completed, failed)"
          - name: "provider"
            type: "String(50)"
            constraints: ["NOT NULL"]
            description: "LLM provider used"
          - name: "prompt_used"
            type: "Text"
            constraints: []
            description: "Prompt used for OCR"
          - name: "raw_response"
            type: "Text"
            constraints: []
            description: "Raw LLM response"
          - name: "processing_time_seconds"
            type: "Float"
            constraints: []
            description: "Processing time in seconds"
          - name: "error_message"
            type: "Text"
            constraints: []
            description: "Error message if failed"
          - name: "retry_count"
            type: "Integer"
            constraints: ["NOT NULL", "DEFAULT 0"]
            description: "Number of retry attempts"
          - name: "total_regions_detected"
            type: "Integer"
            constraints: ["NOT NULL", "DEFAULT 0"]
            description: "Number of text regions detected"
          - name: "average_confidence"
            type: "Float"
            constraints: []
            description: "Average confidence score"
        relationships:
          - target: "project_page"
            type: "many_to_one"
            foreign_key: "page_id"
          - target: "ocr_result"
            type: "one_to_many"
            foreign_key: "job_id"

      ocr_result:
        description: "Individual OCR detection results"
        intent: "Store detected text regions with coordinates and confidence"
        implementation_status: "COMPLETE"
        columns:
          - name: "id"
            type: "String(36)"
            constraints: ["PRIMARY KEY"]
            description: "UUID primary key"
          - name: "created_at"
            type: "DateTime"
            constraints: ["NOT NULL"]
            description: "Creation timestamp"
          - name: "updated_at"
            type: "DateTime"
            constraints: ["NOT NULL"]
            description: "Last update timestamp"
          - name: "job_id"
            type: "String(36)"
            constraints: ["NOT NULL", "FOREIGN KEY"]
            description: "Reference to OCR job"
          - name: "detected_text"
            type: "Text"
            constraints: ["NOT NULL"]
            description: "Detected text content"
          - name: "confidence_score"
            type: "Float"
            constraints: []
            description: "Detection confidence score"
          - name: "region_type"
            type: "String(50)"
            constraints: []
            description: "Type of text region"
          - name: "x"
            type: "Float"
            constraints: []
            description: "X coordinate"
          - name: "y"
            type: "Float"
            constraints: []
            description: "Y coordinate"
          - name: "width"
            type: "Float"
            constraints: []
            description: "Region width"
          - name: "height"
            type: "Float"
            constraints: []
            description: "Region height"
          - name: "metadata"
            type: "JSON"
            constraints: []
            description: "Additional metadata"
        relationships:
          - target: "ocr_job"
            type: "many_to_one"
            foreign_key: "job_id"

      translation_job:
        description: "Translation processing jobs"
        intent: "Track translation requests and results"
        implementation_status: "COMPLETE"
        columns:
          - name: "id"
            type: "String(36)"
            constraints: ["PRIMARY KEY"]
            description: "UUID primary key"
          - name: "created_at"
            type: "DateTime"
            constraints: ["NOT NULL"]
            description: "Creation timestamp"
          - name: "updated_at"
            type: "DateTime"
            constraints: ["NOT NULL"]
            description: "Last update timestamp"
          - name: "text_region_id"
            type: "String(36)"
            constraints: ["NOT NULL", "FOREIGN KEY"]
            description: "Reference to text region"
          - name: "status"
            type: "String(50)"
            constraints: ["NOT NULL"]
            description: "Job status"
          - name: "provider"
            type: "String(50)"
            constraints: ["NOT NULL"]
            description: "LLM provider used"
          - name: "source_language"
            type: "String(50)"
            constraints: ["NOT NULL"]
            description: "Source language"
          - name: "target_language"
            type: "String(50)"
            constraints: ["NOT NULL"]
            description: "Target language"
          - name: "original_text"
            type: "Text"
            constraints: ["NOT NULL"]
            description: "Original text to translate"
          - name: "translated_text"
            type: "Text"
            constraints: []
            description: "Translated text"
          - name: "prompt_used"
            type: "Text"
            constraints: []
            description: "Translation prompt used"
          - name: "raw_response"
            type: "Text"
            constraints: []
            description: "Raw LLM response"
          - name: "processing_time_seconds"
            type: "Float"
            constraints: []
            description: "Processing time"
          - name: "confidence_score"
            type: "Float"
            constraints: []
            description: "Translation confidence"
          - name: "quality_score"
            type: "Float"
            constraints: []
            description: "Translation quality score"
          - name: "error_message"
            type: "Text"
            constraints: []
            description: "Error message if failed"
          - name: "retry_count"
            type: "Integer"
            constraints: ["NOT NULL", "DEFAULT 0"]
            description: "Retry attempts"
          - name: "metadata"
            type: "JSON"
            constraints: []
            description: "Additional metadata"
        relationships:
          - target: "text_region"
            type: "many_to_one"
            foreign_key: "text_region_id"
          - target: "translation_alternative"
            type: "one_to_many"
            foreign_key: "job_id"

      translation_alternative:
        description: "Alternative translation options"
        intent: "Store multiple translation options for user selection"
        implementation_status: "COMPLETE"
        columns:
          - name: "id"
            type: "String(36)"
            constraints: ["PRIMARY KEY"]
            description: "UUID primary key"
          - name: "created_at"
            type: "DateTime"
            constraints: ["NOT NULL"]
            description: "Creation timestamp"
          - name: "updated_at"
            type: "DateTime"
            constraints: ["NOT NULL"]
            description: "Last update timestamp"
          - name: "job_id"
            type: "String(36)"
            constraints: ["NOT NULL", "FOREIGN KEY"]
            description: "Reference to translation job"
          - name: "translated_text"
            type: "Text"
            constraints: ["NOT NULL"]
            description: "Alternative translation"
          - name: "confidence_score"
            type: "Float"
            constraints: []
            description: "Confidence score"
          - name: "quality_score"
            type: "Float"
            constraints: []
            description: "Quality score"
          - name: "rank"
            type: "Integer"
            constraints: ["NOT NULL"]
            description: "Ranking order"
          - name: "is_selected"
            type: "String(5)"
            constraints: ["NOT NULL", "DEFAULT 'false'"]
            description: "Whether selected (SQLite boolean)"
          - name: "metadata"
            type: "JSON"
            constraints: []
            description: "Additional metadata"
        relationships:
          - target: "translation_job"
            type: "many_to_one"
            foreign_key: "job_id"

      translation_template:
        description: "Reusable translation templates"
        intent: "Store translation patterns for common phrases"
        implementation_status: "COMPLETE"
        columns:
          - name: "id"
            type: "String(36)"
            constraints: ["PRIMARY KEY"]
            description: "UUID primary key"
          - name: "created_at"
            type: "DateTime"
            constraints: ["NOT NULL"]
            description: "Creation timestamp"
          - name: "updated_at"
            type: "DateTime"
            constraints: ["NOT NULL"]
            description: "Last update timestamp"
          - name: "name"
            type: "String(255)"
            constraints: ["NOT NULL"]
            description: "Template name"
          - name: "description"
            type: "Text"
            constraints: []
            description: "Template description"
          - name: "source_language"
            type: "String(50)"
            constraints: ["NOT NULL"]
            description: "Source language"
          - name: "target_language"
            type: "String(50)"
            constraints: ["NOT NULL"]
            description: "Target language"
          - name: "source_pattern"
            type: "Text"
            constraints: ["NOT NULL"]
            description: "Source text pattern"
          - name: "target_pattern"
            type: "Text"
            constraints: ["NOT NULL"]
            description: "Target text pattern"
          - name: "usage_count"
            type: "Integer"
            constraints: ["NOT NULL", "DEFAULT 0"]
            description: "Usage count"
          - name: "category"
            type: "String(100)"
            constraints: []
            description: "Template category"
          - name: "tags"
            type: "JSON"
            constraints: []
            description: "Template tags"
          - name: "average_quality_score"
            type: "Float"
            constraints: []
            description: "Average quality score"

  migrations:
    description: "Database schema migration management"
    intent: "Version control for database schema changes"
    implementation_status: "COMPLETE"
    implementation_gaps: []
    depends_on:
      - "configuration"
    files:
      - "alembic.ini"
      - "alembic/env.py"
      - "alembic/script.py.mako"
      - "alembic/versions/0001_initial_schema.py"
    features:
      - "Alembic migration framework"
      - "Automatic migration generation"
      - "Version tracking"
      - "Rollback support"
      - "Environment-specific configurations"

  utilities:
    description: "Database utility functions and management tools"
    intent: "Provide tools for database initialization, seeding, and maintenance"
    implementation_status: "COMPLETE"
    implementation_gaps: []
    depends_on:
      - "configuration"
      - "migrations"
    files:
      - "src/database_init.py"
    features:
      - "Database initialization script"
      - "Sample data seeding"
      - "Database health checks"
      - "Schema validation"
      - "Data migration utilities"
