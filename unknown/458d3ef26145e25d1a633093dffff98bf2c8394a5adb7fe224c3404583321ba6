/**
 * Utility functions for API client operations
 */

import { APIError } from './api-client';

// Request timeout configuration
export const REQUEST_TIMEOUTS = {
  DEFAULT: 30000, // 30 seconds
  UPLOAD: 120000, // 2 minutes for file uploads
  OCR: 180000, // 3 minutes for OCR processing
  TRANSLATION: 120000, // 2 minutes for translation
  HEALTH_CHECK: 5000 // 5 seconds for health checks
} as const;

// Abort controller manager for request cancellation
export class RequestManager {
  private controllers = new Map<string, AbortController>();

  createRequest(id: string, timeout: number = REQUEST_TIMEOUTS.DEFAULT): AbortSignal {
    // Cancel existing request with same ID
    this.cancelRequest(id);

    const controller = new AbortController();
    this.controllers.set(id, controller);

    // Set timeout
    const timeoutId = setTimeout(() => {
      controller.abort();
      this.controllers.delete(id);
    }, timeout);

    // Clean up timeout when request completes
    controller.signal.addEventListener('abort', () => {
      clearTimeout(timeoutId);
    });

    return controller.signal;
  }

  cancelRequest(id: string): void {
    const controller = this.controllers.get(id);
    if (controller) {
      controller.abort();
      this.controllers.delete(id);
    }
  }

  cancelAllRequests(): void {
    for (const controller of this.controllers.values()) {
      controller.abort();
    }
    this.controllers.clear();
  }

  isRequestActive(id: string): boolean {
    return this.controllers.has(id);
  }
}

// Global request manager instance
export const requestManager = new RequestManager();

// Error handling utilities
export function isAPIError(error: unknown): error is APIError {
  return error instanceof APIError;
}

export function getErrorMessage(error: unknown): string {
  if (isAPIError(error)) {
    return error.detail;
  }

  if (error instanceof Error) {
    return error.message;
  }

  return 'An unknown error occurred';
}

export function getErrorCode(error: unknown): string {
  if (isAPIError(error)) {
    return error.errorCode;
  }

  return 'UNKNOWN_ERROR';
}

export function isNetworkError(error: unknown): boolean {
  return isAPIError(error) && error.status === 0;
}

export function isTimeoutError(error: unknown): boolean {
  return error instanceof Error && error.name === 'AbortError';
}

export function isServerError(error: unknown): boolean {
  return isAPIError(error) && error.status >= 500;
}

export function isClientError(error: unknown): boolean {
  return isAPIError(error) && error.status >= 400 && error.status < 500;
}

// Retry logic for failed requests
export interface RetryOptions {
  maxAttempts: number;
  baseDelay: number; // milliseconds
  maxDelay: number; // milliseconds
  backoffFactor: number;
  retryCondition?: (error: unknown) => boolean;
}

export const DEFAULT_RETRY_OPTIONS: RetryOptions = {
  maxAttempts: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2,
  retryCondition: (error) => isNetworkError(error) || isServerError(error)
};

export async function withRetry<T>(
  operation: () => Promise<T>,
  options: Partial<RetryOptions> = {}
): Promise<T> {
  const config = { ...DEFAULT_RETRY_OPTIONS, ...options };
  let lastError: unknown;

  for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;

      // Don't retry if condition is not met or it's the last attempt
      if (!config.retryCondition?.(error) || attempt === config.maxAttempts) {
        throw error;
      }

      // Calculate delay with exponential backoff
      const delay = Math.min(
        config.baseDelay * Math.pow(config.backoffFactor, attempt - 1),
        config.maxDelay
      );

      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}

// Progress tracking for uploads and long-running operations
export interface ProgressCallback {
  (progress: number): void;
}

export class ProgressTracker {
  private callbacks = new Set<ProgressCallback>();
  private _progress = 0;

  get progress(): number {
    return this._progress;
  }

  addCallback(callback: ProgressCallback): void {
    this.callbacks.add(callback);
  }

  removeCallback(callback: ProgressCallback): void {
    this.callbacks.delete(callback);
  }

  updateProgress(progress: number): void {
    this._progress = Math.max(0, Math.min(100, progress));
    this.callbacks.forEach(callback => callback(this._progress));
  }

  reset(): void {
    this._progress = 0;
    this.callbacks.forEach(callback => callback(0));
  }

  complete(): void {
    this.updateProgress(100);
  }
}

// File upload utilities
export function validateImageFile(file: File): { isValid: boolean; error?: string } {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  const maxSize = 50 * 1024 * 1024; // 50MB

  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `Invalid file type. Allowed types: ${allowedTypes.join(', ')}`
    };
  }

  if (file.size > maxSize) {
    return {
      isValid: false,
      error: `File too large. Maximum size: ${maxSize / (1024 * 1024)}MB`
    };
  }

  return { isValid: true };
}

export function createImagePreview(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsDataURL(file);
  });
}

// URL utilities
export function buildImageUrl(pageId: string): string {
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
  return `${baseUrl}/api/v1/projects/pages/${pageId}/image`;
}

export function buildThumbnailUrl(pageId: string, size: number = 200): string {
  // For now, use the same image endpoint (thumbnail generation can be added later)
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
  return `${baseUrl}/api/v1/projects/pages/${pageId}/image`;
}

// Cache utilities for API responses
export class APICache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  set<T>(key: string, data: T, ttlMs: number = 300000): void { // 5 minutes default
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlMs
    });
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data as T;
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }
}

// Global cache instance
export const apiCache = new APICache();

// Request deduplication to prevent duplicate API calls
export class RequestDeduplicator {
  private pendingRequests = new Map<string, Promise<any>>();

  async deduplicate<T>(key: string, operation: () => Promise<T>): Promise<T> {
    if (this.pendingRequests.has(key)) {
      return this.pendingRequests.get(key) as Promise<T>;
    }

    const promise = operation().finally(() => {
      this.pendingRequests.delete(key);
    });

    this.pendingRequests.set(key, promise);
    return promise;
  }

  cancel(key: string): void {
    this.pendingRequests.delete(key);
  }

  cancelAll(): void {
    this.pendingRequests.clear();
  }
}

// Global deduplicator instance
export const requestDeduplicator = new RequestDeduplicator();
