---
# Modules System Definitions
# Domain-specific modules and their implementations

modules:
  description: "Domain-driven modules implementing manga translation workflow"
  intent: "Organize business logic into cohesive, loosely-coupled modules following Netflix Dispatch architecture"
  
  projects:
    description: "Project management module for manga translation projects"
    intent: "Handle project lifecycle, page management, and text region coordination"
    implementation_status: "COMPLETE"
    implementation_gaps: []
    depends_on:
      - "database"
      - "pagination"
      - "file_storage"
    files:
      - "src/projects/"
      - "src/projects/models.py"
      - "src/projects/schemas.py"
      - "src/projects/service.py"
      - "src/projects/router.py"
      - "src/projects/exceptions.py"
    
    components:
      models:
        description: "SQLAlchemy models for project domain"
        implementation_status: "COMPLETE"
        entities:
          - "Project - Main project entity"
          - "ProjectPage - Individual manga pages"
          - "TextRegion - Text regions within pages"
        relationships:
          - "Project -> ProjectPage (one-to-many)"
          - "ProjectPage -> TextRegion (one-to-many)"
          - "ProjectPage -> OCRJob (one-to-many)"
          - "TextRegion -> TranslationJob (one-to-many)"
      
      schemas:
        description: "Pydantic schemas for API validation"
        implementation_status: "COMPLETE"
        schemas:
          - "ProjectCreate, ProjectUpdate, ProjectResponse"
          - "ProjectPageCreate, ProjectPageUpdate, ProjectPageResponse"
          - "TextRegionCreate, TextRegionUpdate, TextRegionResponse"
          - "ProjectDetailResponse, ProjectPageDetailResponse"
      
      service:
        description: "Business logic for project operations"
        implementation_status: "COMPLETE"
        features:
          - "Project CRUD operations"
          - "Page upload and management"
          - "Text region management"
          - "Paginated project listing"
          - "Project statistics"
          - "File handling integration"
        methods:
          - "create_project, get_project, update_project, delete_project"
          - "get_projects_paginated"
          - "upload_page, get_page, update_page, delete_page"
          - "create_text_region, get_text_region, update_text_region"
          - "get_project_detail, get_page_detail"
      
      router:
        description: "FastAPI router for project endpoints"
        implementation_status: "COMPLETE"
        endpoints:
          - "GET /projects - List projects with pagination"
          - "POST /projects - Create new project"
          - "GET /projects/{id} - Get project by ID"
          - "PUT /projects/{id} - Update project"
          - "DELETE /projects/{id} - Delete project"
          - "GET /projects/{id}/detail - Get project with pages"
          - "POST /projects/{id}/pages - Upload page"
          - "GET /projects/{id}/pages - List project pages"
          - "Page and text region management endpoints"
      
      exceptions:
        description: "Domain-specific exceptions"
        implementation_status: "COMPLETE"
        exceptions:
          - "ProjectNotFound, ProjectPageNotFound, TextRegionNotFound"
          - "ProjectNameConflict, PageNumberConflict"
          - "InvalidFileFormat, FileTooLarge"

  ocr:
    description: "OCR processing module for text detection and extraction"
    intent: "Coordinate LLM-based OCR processing with job management and result storage"
    implementation_status: "COMPLETE"
    implementation_gaps:
      - "Background job processing"
      - "Job queue management"
      - "Batch processing optimization"
    depends_on:
      - "database"
      - "llm_providers"
      - "projects"
    files:
      - "src/ocr/"
      - "src/ocr/models.py"
      - "src/ocr/schemas.py"
      - "src/ocr/service.py"
      - "src/ocr/router.py"
      - "src/ocr/exceptions.py"
    
    components:
      models:
        description: "SQLAlchemy models for OCR domain"
        implementation_status: "COMPLETE"
        entities:
          - "OCRJob - OCR processing jobs"
          - "OCRResult - Individual detection results"
        relationships:
          - "OCRJob -> ProjectPage (many-to-one)"
          - "OCRJob -> OCRResult (one-to-many)"
      
      schemas:
        description: "Pydantic schemas for OCR operations"
        implementation_status: "COMPLETE"
        schemas:
          - "OCRJobCreate, OCRJobResponse, OCRJobDetailResponse"
          - "OCRResultResponse"
          - "OCRProcessRequest, OCRBatchProcessRequest"
          - "OCRStatistics"
      
      service:
        description: "Business logic for OCR operations"
        implementation_status: "COMPLETE"
        features:
          - "OCR job creation and management"
          - "LLM provider integration"
          - "Result processing and storage"
          - "Job status tracking"
          - "Error handling and retry logic"
          - "Statistics and reporting"
        methods:
          - "create_ocr_job, get_ocr_job, get_ocr_job_detail"
          - "process_ocr_request, process_batch_ocr"
          - "get_ocr_results, get_ocr_jobs_by_page"
          - "get_ocr_statistics"
          - "retry_failed_job"
      
      router:
        description: "FastAPI router for OCR endpoints"
        implementation_status: "COMPLETE"
        endpoints:
          - "POST /ocr/jobs - Create OCR job"
          - "GET /ocr/jobs/{id} - Get OCR job"
          - "GET /ocr/jobs/{id}/detail - Get job with results"
          - "GET /ocr/jobs/{id}/results - Get OCR results"
          - "POST /ocr/process - Process OCR request"
          - "POST /ocr/process/batch - Batch OCR processing"
          - "GET /ocr/statistics - Get OCR statistics"
          - "POST /ocr/jobs/{id}/retry - Retry failed job"
      
      exceptions:
        description: "OCR-specific exceptions"
        implementation_status: "COMPLETE"
        exceptions:
          - "OCRJobNotFound, OCRResultNotFound"
          - "OCRProcessingFailed, OCRProviderUnavailable"
          - "InvalidOCRStatus"

  translation:
    description: "Translation processing module for text translation with alternatives"
    intent: "Coordinate LLM-based translation with template management and quality scoring"
    implementation_status: "COMPLETE"
    implementation_gaps:
      - "Translation quality scoring algorithms"
      - "Translation memory integration"
      - "Batch translation optimization"
      - "Template matching algorithms"
    depends_on:
      - "database"
      - "llm_providers"
      - "projects"
      - "pagination"
    files:
      - "src/translation/"
      - "src/translation/models.py"
      - "src/translation/schemas.py"
      - "src/translation/service.py"
      - "src/translation/router.py"
      - "src/translation/exceptions.py"
    
    components:
      models:
        description: "SQLAlchemy models for translation domain"
        implementation_status: "COMPLETE"
        entities:
          - "TranslationJob - Translation processing jobs"
          - "TranslationAlternative - Alternative translations"
          - "TranslationTemplate - Reusable translation patterns"
        relationships:
          - "TranslationJob -> TextRegion (many-to-one)"
          - "TranslationJob -> TranslationAlternative (one-to-many)"
      
      schemas:
        description: "Pydantic schemas for translation operations"
        implementation_status: "COMPLETE"
        schemas:
          - "TranslationJobCreate, TranslationJobResponse, TranslationJobDetailResponse"
          - "TranslationAlternativeCreate, TranslationAlternativeResponse"
          - "TranslationTemplateCreate, TranslationTemplateUpdate, TranslationTemplateResponse"
          - "TranslationProcessRequest, TranslationBatchProcessRequest"
          - "TranslationStatistics, LLMTranslationResponse"
      
      service:
        description: "Business logic for translation operations"
        implementation_status: "COMPLETE"
        features:
          - "Translation job creation and management"
          - "Alternative translation generation"
          - "Translation template management"
          - "LLM provider integration"
          - "Quality scoring and ranking"
          - "Paginated template listing"
          - "Statistics and reporting"
        methods:
          - "create_translation_job, get_translation_job, get_translation_job_detail"
          - "process_translation_request, process_batch_translation"
          - "get_translation_alternatives, select_translation_alternative"
          - "create_translation_template, get_translation_templates_paginated"
          - "update_translation_template, delete_translation_template"
          - "get_translation_statistics"
      
      router:
        description: "FastAPI router for translation endpoints"
        implementation_status: "COMPLETE"
        endpoints:
          - "POST /translation/jobs - Create translation job"
          - "GET /translation/jobs/{id} - Get translation job"
          - "GET /translation/jobs/{id}/detail - Get job with alternatives"
          - "GET /translation/jobs/{id}/alternatives - Get alternatives"
          - "POST /translation/jobs/{id}/alternatives/{alt_id}/select - Select alternative"
          - "POST /translation/process - Process translation request"
          - "POST /translation/process/batch - Batch translation"
          - "Template management endpoints"
          - "GET /translation/statistics - Get statistics"
      
      exceptions:
        description: "Translation-specific exceptions"
        implementation_status: "COMPLETE"
        exceptions:
          - "TranslationJobNotFound, TranslationAlternativeNotFound"
          - "TranslationTemplateNotFound, TranslationFailed"
          - "UnsupportedLanguagePair, TemplateNameConflict"

  llm_providers:
    description: "LLM provider integration module for AI capabilities"
    intent: "Abstract LLM provider interactions with unified interface and error handling"
    implementation_status: "COMPLETE"
    implementation_gaps:
      - "Provider health monitoring"
      - "Model performance metrics"
      - "Automatic failover between providers"
    depends_on:
      - "configuration"
    files:
      - "src/llm_providers/"
      - "src/llm_providers/base.py"
      - "src/llm_providers/claude.py"
      - "src/llm_providers/openai.py"
      - "src/llm_providers/gemini.py"
      - "src/llm_providers/deepseek.py"
      - "src/llm_providers/factory.py"
      - "src/llm_providers/service.py"
      - "src/llm_providers/router.py"
      - "src/llm_providers/schemas.py"
      - "src/llm_providers/exceptions.py"
    
    components:
      base:
        description: "Abstract base class for LLM providers"
        implementation_status: "COMPLETE"
        features:
          - "Unified interface for all providers"
          - "Common error handling patterns"
          - "Response standardization"
          - "Rate limiting support"
          - "Retry logic"
        methods:
          - "generate_text, analyze_image, perform_ocr, perform_translation"
          - "get_available_models, get_default_model"
          - "validate_request, handle_response"
      
      providers:
        description: "Concrete LLM provider implementations"
        implementation_status: "COMPLETE"
        providers:
          claude:
            description: "Anthropic Claude integration"
            models: ["claude-3-5-sonnet-20241022", "claude-3-haiku-20240307"]
            capabilities: ["text_generation", "image_analysis", "ocr", "translation"]
          openai:
            description: "OpenAI GPT integration"
            models: ["gpt-4o", "gpt-4o-mini"]
            capabilities: ["text_generation", "image_analysis", "ocr", "translation"]
          gemini:
            description: "Google Gemini integration"
            models: ["gemini-1.5-pro", "gemini-1.5-flash"]
            capabilities: ["text_generation", "image_analysis", "ocr", "translation"]
          deepseek:
            description: "Deepseek integration"
            models: ["deepseek-chat", "deepseek-coder", "deepseek-reasoner"]
            capabilities: ["text_generation", "image_analysis", "ocr", "translation"]
      
      factory:
        description: "Provider factory and management"
        implementation_status: "COMPLETE"
        features:
          - "Provider instantiation"
          - "Availability checking"
          - "Configuration validation"
          - "Provider selection logic"
        methods:
          - "create_client, get_available_providers"
          - "is_provider_available, get_provider_info"
      
      service:
        description: "High-level LLM service orchestration"
        implementation_status: "COMPLETE"
        features:
          - "Provider abstraction"
          - "Request routing"
          - "Error handling and fallback"
          - "Response processing"
        methods:
          - "generate_text, analyze_image, perform_ocr, perform_translation"
          - "get_providers_status"
      
      router:
        description: "FastAPI router for LLM endpoints"
        implementation_status: "COMPLETE"
        endpoints:
          - "GET /llm/providers - Get providers status"
          - "GET /llm/providers/{provider}/models - Get provider models"
          - "POST /llm/generate/text - Generate text"
          - "POST /llm/generate/batch - Batch text generation"
          - "POST /llm/analyze/image - Analyze image"
          - "POST /llm/ocr - Perform OCR"
          - "POST /llm/translate - Translate text"
      
      schemas:
        description: "Pydantic schemas for LLM operations"
        implementation_status: "COMPLETE"
        schemas:
          - "LLMTextGenerationRequest, LLMImageAnalysisRequest"
          - "LLMOCRRequest, LLMTranslationRequest"
          - "LLMGenerationResponse, LLMBatchResponse"
          - "LLMProvidersStatus, LLMProviderInfo"
      
      exceptions:
        description: "LLM provider specific exceptions"
        implementation_status: "COMPLETE"
        exceptions:
          - "LLMProviderNotAvailable, LLMAPIError"
          - "LLMRateLimitExceeded, LLMQuotaExceeded"
          - "InvalidLLMModel, LLMTimeout"

  integration:
    description: "Cross-module integration and coordination"
    intent: "Coordinate workflows across multiple modules"
    implementation_status: "PARTIAL"
    implementation_gaps:
      - "Workflow orchestration"
      - "Event-driven communication"
      - "Background job processing"
    depends_on:
      - "projects"
      - "ocr"
      - "translation"
      - "llm_providers"
    
    planned_components:
      workflow_engine:
        description: "Orchestrate multi-step workflows"
        intent: "Coordinate OCR -> Translation workflows"
        features:
          - "Workflow definition and execution"
          - "Step dependency management"
          - "Error handling and recovery"
          - "Progress tracking"
      
      event_system:
        description: "Event-driven communication between modules"
        intent: "Decouple modules with event-based messaging"
        features:
          - "Event publishing and subscription"
          - "Async event processing"
          - "Event persistence and replay"
          - "Dead letter queue handling"
      
      job_queue:
        description: "Background job processing system"
        intent: "Handle long-running tasks asynchronously"
        features:
          - "Job scheduling and execution"
          - "Priority queues"
          - "Job retry and failure handling"
          - "Progress monitoring"

  testing:
    description: "Comprehensive testing infrastructure for all modules"
    intent: "Ensure code quality and prevent regressions"
    implementation_status: "COMPLETE"
    implementation_gaps: []
    depends_on:
      - "all_modules"
    files:
      - "tests/"
      - "tests/conftest.py"
      - "tests/utils.py"
      - "tests/projects/test_projects_api.py"
      - "tests/ocr/test_ocr_api.py"
      - "tests/translation/test_translation_api.py"
      - "tests/llm_providers/test_llm_api.py"
      - "tests/test_pagination.py"
    
    components:
      test_infrastructure:
        description: "Testing framework and utilities"
        implementation_status: "COMPLETE"
        features:
          - "Async test client setup (prevents event loop issues)"
          - "Mock database utilities"
          - "Test fixtures for all domains"
          - "Coverage reporting"
      
      module_tests:
        description: "Module-specific test suites"
        implementation_status: "COMPLETE"
        coverage:
          - "Projects module: API endpoints, service methods, error handling"
          - "OCR module: Job processing, result handling, provider integration"
          - "Translation module: Job processing, alternatives, templates"
          - "LLM providers: Provider abstraction, error handling, responses"
          - "Pagination: Parameter validation, response formatting, database integration"
