"""
Translation-specific exceptions.
"""
from src.constants import ErrorCode
from src.exceptions import NotFoundError, BadRequestError, InternalServerError


class TranslationJobNotFound(NotFoundError):
    """Translation job not found exception."""
    
    def __init__(self, detail: str = "Translation job not found"):
        super().__init__(detail=detail, error_code="TRANSLATION_JOB_NOT_FOUND")


class TranslationTemplateNotFound(NotFoundError):
    """Translation template not found exception."""
    
    def __init__(self, detail: str = "Translation template not found"):
        super().__init__(detail=detail, error_code="TRANSLATION_TEMPLATE_NOT_FOUND")


class TranslationFailed(InternalServerError):
    """Translation processing failed exception."""
    
    def __init__(self, detail: str = "Translation processing failed"):
        super().__init__(detail=detail, error_code=ErrorCode.TRANSLATION_FAILED)


class UnsupportedLanguagePair(BadRequestError):
    """Unsupported language pair exception."""
    
    def __init__(self, detail: str = "Unsupported language pair"):
        super().__init__(detail=detail, error_code=ErrorCode.UNSUPPORTED_LANGUAGE)


class TranslationAlternativeNotFound(NotFoundError):
    """Translation alternative not found exception."""
    
    def __init__(self, detail: str = "Translation alternative not found"):
        super().__init__(detail=detail, error_code="TRANSLATION_ALTERNATIVE_NOT_FOUND")
