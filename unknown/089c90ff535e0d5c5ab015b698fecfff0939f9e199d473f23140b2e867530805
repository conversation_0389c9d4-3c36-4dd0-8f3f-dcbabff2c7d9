"""
Integration tests for projects API endpoints.
"""
import pytest
from httpx import AsyncClient
from unittest.mock import AsyncMock, patch

from src.pagination import PaginatedResponse


@pytest.mark.asyncio
async def test_create_project(client: AsyncClient, sample_project_data):
    """Test creating a new project."""
    with patch('src.projects.service.ProjectService.create_project') as mock_create:
        # Mock the service response
        mock_create.return_value = {
            "id": "test-project-id",
            "name": sample_project_data["name"],
            "description": sample_project_data["description"],
            "status": "draft",
            "source_language": sample_project_data["source_language"],
            "target_language": sample_project_data["target_language"],
            "page_count": 0,
            "created_at": "2025-06-30T10:00:00Z",
            "updated_at": "2025-06-30T10:00:00Z"
        }
        
        response = await client.post("/api/v1/projects", json=sample_project_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == sample_project_data["name"]
        assert data["status"] == "draft"
        assert data["page_count"] == 0


@pytest.mark.asyncio
async def test_get_projects_paginated(client: AsyncClient):
    """Test getting projects with pagination."""
    with patch('src.projects.service.ProjectService.get_projects_paginated') as mock_get:
        # Mock paginated response
        mock_projects = [
            {
                "id": "project-1",
                "name": "Project 1",
                "description": "Test project 1",
                "status": "draft",
                "source_language": "japanese",
                "target_language": "english",
                "page_count": 5,
                "created_at": "2025-06-30T10:00:00Z",
                "updated_at": "2025-06-30T10:00:00Z"
            },
            {
                "id": "project-2",
                "name": "Project 2",
                "description": "Test project 2",
                "status": "in_progress",
                "source_language": "japanese",
                "target_language": "english",
                "page_count": 3,
                "created_at": "2025-06-30T11:00:00Z",
                "updated_at": "2025-06-30T11:00:00Z"
            }
        ]
        
        mock_response = {
            "items": mock_projects,
            "meta": {
                "page": 1,
                "limit": 10,
                "total": 2,
                "pages": 1,
                "has_prev": False,
                "has_next": False,
                "prev_page": None,
                "next_page": None
            }
        }
        mock_get.return_value = mock_response
        
        response = await client.get("/api/v1/projects?page=1&limit=10")
        
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert "meta" in data
        assert len(data["items"]) == 2
        assert data["meta"]["total"] == 2
        assert data["meta"]["page"] == 1


@pytest.mark.asyncio
async def test_get_project_by_id(client: AsyncClient):
    """Test getting a specific project by ID."""
    project_id = "test-project-id"
    
    with patch('src.projects.service.ProjectService.get_project') as mock_get:
        mock_project = {
            "id": project_id,
            "name": "Test Project",
            "description": "A test project",
            "status": "draft",
            "source_language": "japanese",
            "target_language": "english",
            "page_count": 0,
            "created_at": "2025-06-30T10:00:00Z",
            "updated_at": "2025-06-30T10:00:00Z"
        }
        mock_get.return_value = mock_project
        
        response = await client.get(f"/api/v1/projects/{project_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == project_id
        assert data["name"] == "Test Project"


@pytest.mark.asyncio
async def test_update_project(client: AsyncClient):
    """Test updating a project."""
    project_id = "test-project-id"
    update_data = {
        "name": "Updated Project Name",
        "status": "in_progress"
    }
    
    with patch('src.projects.service.ProjectService.update_project') as mock_update:
        mock_updated_project = {
            "id": project_id,
            "name": update_data["name"],
            "description": "A test project",
            "status": update_data["status"],
            "source_language": "japanese",
            "target_language": "english",
            "page_count": 0,
            "created_at": "2025-06-30T10:00:00Z",
            "updated_at": "2025-06-30T12:00:00Z"
        }
        mock_update.return_value = mock_updated_project
        
        response = await client.put(f"/api/v1/projects/{project_id}", json=update_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["status"] == update_data["status"]


@pytest.mark.asyncio
async def test_delete_project(client: AsyncClient):
    """Test deleting a project."""
    project_id = "test-project-id"
    
    with patch('src.projects.service.ProjectService.delete_project') as mock_delete:
        mock_delete.return_value = True
        
        response = await client.delete(f"/api/v1/projects/{project_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Project deleted successfully"


@pytest.mark.asyncio
async def test_get_project_detail(client: AsyncClient):
    """Test getting detailed project information with pages."""
    project_id = "test-project-id"
    
    with patch('src.projects.service.ProjectService.get_project_detail') as mock_get_detail:
        mock_detail = {
            "id": project_id,
            "name": "Test Project",
            "description": "A test project",
            "status": "draft",
            "source_language": "japanese",
            "target_language": "english",
            "page_count": 2,
            "created_at": "2025-06-30T10:00:00Z",
            "updated_at": "2025-06-30T10:00:00Z",
            "pages": [
                {
                    "id": "page-1",
                    "project_id": project_id,
                    "page_number": 1,
                    "original_filename": "page_001.jpg",
                    "file_path": "/uploads/page_001.jpg",
                    "file_size": 1024000,
                    "image_width": 800,
                    "image_height": 1200,
                    "ocr_status": "pending",
                    "text_region_count": 0,
                    "created_at": "2025-06-30T10:00:00Z",
                    "updated_at": "2025-06-30T10:00:00Z"
                }
            ]
        }
        mock_get_detail.return_value = mock_detail
        
        response = await client.get(f"/api/v1/projects/{project_id}/detail")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == project_id
        assert "pages" in data
        assert len(data["pages"]) == 1


@pytest.mark.asyncio
async def test_project_not_found(client: AsyncClient):
    """Test handling of non-existent project."""
    project_id = "non-existent-id"
    
    with patch('src.projects.service.ProjectService.get_project') as mock_get:
        from src.projects.exceptions import ProjectNotFound
        mock_get.side_effect = ProjectNotFound("Project not found")
        
        response = await client.get(f"/api/v1/projects/{project_id}")
        
        assert response.status_code == 404


@pytest.mark.asyncio
async def test_create_project_validation_error(client: AsyncClient):
    """Test project creation with invalid data."""
    invalid_data = {
        "name": "",  # Empty name should fail validation
        "source_language": "invalid_language"
    }
    
    response = await client.post("/api/v1/projects", json=invalid_data)
    
    assert response.status_code == 422  # Validation error


@pytest.mark.asyncio
async def test_pagination_parameters(client: AsyncClient):
    """Test various pagination parameters."""
    with patch('src.projects.service.ProjectService.get_projects_paginated') as mock_get:
        mock_response = {
            "items": [],
            "meta": {
                "page": 2,
                "limit": 5,
                "total": 0,
                "pages": 1,
                "has_prev": True,
                "has_next": False,
                "prev_page": 1,
                "next_page": None
            }
        }
        mock_get.return_value = mock_response
        
        # Test custom pagination parameters
        response = await client.get("/api/v1/projects?page=2&limit=5")
        
        assert response.status_code == 200
        data = response.json()
        assert data["meta"]["page"] == 2
        assert data["meta"]["limit"] == 5


@pytest.mark.asyncio
async def test_pagination_limit_validation(client: AsyncClient):
    """Test pagination limit validation."""
    # Test limit exceeding maximum
    response = await client.get("/api/v1/projects?page=1&limit=101")
    
    assert response.status_code == 422  # Validation error
    
    # Test invalid page number
    response = await client.get("/api/v1/projects?page=0&limit=10")
    
    assert response.status_code == 422  # Validation error
