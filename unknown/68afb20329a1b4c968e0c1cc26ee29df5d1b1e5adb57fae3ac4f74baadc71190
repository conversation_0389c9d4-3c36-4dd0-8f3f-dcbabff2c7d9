"""
Dependencies for project management endpoints.
"""
from typing import Dict, Any

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from src.database import get_db
from src.projects.service import ProjectService
from src.projects.schemas import ProjectResponse, ProjectPageResponse, TextRegionResponse


async def get_project_service(session: AsyncSession = Depends(get_db)) -> ProjectService:
    """Dependency to get project service instance."""
    return ProjectService(session)


async def valid_project_id(
    project_id: str,
    service: ProjectService = Depends(get_project_service)
) -> ProjectResponse:
    """Dependency to validate project ID and return project data."""
    return await service.get_project(project_id)


async def valid_project_page_id(
    page_id: str,
    service: ProjectService = Depends(get_project_service)
) -> ProjectPageResponse:
    """Dependency to validate project page ID and return page data."""
    return await service.get_project_page(page_id)


async def valid_text_region_id(
    region_id: str,
    service: ProjectService = Depends(get_project_service)
) -> TextRegionResponse:
    """Dependency to validate text region ID and return region data."""
    return await service.get_text_region(region_id)


async def valid_project_and_page(
    project_id: str,
    page_id: str,
    service: ProjectService = Depends(get_project_service)
) -> Dict[str, Any]:
    """Dependency to validate both project and page IDs."""
    project = await service.get_project(project_id)
    page = await service.get_project_page(page_id)

    # Verify page belongs to project
    if page.project_id != project_id:
        from src.projects.exceptions import ProjectPageNotFound
        raise ProjectPageNotFound(
            "Page does not belong to the specified project")

    return {"project": project, "page": page}


async def valid_page_and_region(
    page_id: str,
    region_id: str,
    service: ProjectService = Depends(get_project_service)
) -> Dict[str, Any]:
    """Dependency to validate both page and text region IDs."""
    page = await service.get_project_page(page_id)
    region = await service.get_text_region(region_id)

    # Verify region belongs to page
    if region.page_id != page_id:
        from src.projects.exceptions import TextRegionNotFound
        raise TextRegionNotFound(
            "Text region does not belong to the specified page")

    return {"page": page, "region": region}
