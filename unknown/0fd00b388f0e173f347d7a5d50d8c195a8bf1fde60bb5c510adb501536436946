'use client';

import { Html } from 'react-konva-utils';
import { useEffect, useRef } from 'react';
import Konva from 'konva';

// Fix text rendering issues
(Konva as any)._fixTextRendering = true;

interface InlineTextEditorProps {
  textNode: any; // Konva Text node
  region: any; // Text region data with dimensions
  initialText?: string; // Initial text content to display
  onClose: () => void;
  onChange: (text: string) => void;
  onResize?: (newDimensions: { width: number; height: number }) => void; // Callback for resizing
}

export default function InlineTextEditor({ textNode, region, initialText, onClose, onChange, onResize }: InlineTextEditorProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const measureCanvasRef = useRef<HTMLCanvasElement | null>(null);
  const resizeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Match the exact positioning of the Konva Text element
  const regionWidth = region?.width || 100;
  const regionHeight = region?.height || 50;
  const regionX = region?.x || 0;
  const regionY = region?.y || 0;

  // Konva Text has x={4}, y={4} offset and width/height reduced by 8px
  const textX = 4;
  const textY = 4;
  const textWidth = regionWidth - 8;
  const textHeight = regionHeight - 8;

  const fontSize = region?.font_size || Math.min(14, regionHeight / 3);
  const fontFamily = region?.font_family || 'Arial';
  const textAlign = 'center'; // Match Konva Text align="center"

  // For center alignment with middle vertical alignment, we need to calculate padding
  // to match Konva's verticalAlign="middle" behavior
  const lineHeight = 1.2;
  const estimatedTextHeight = fontSize * lineHeight;
  const verticalPadding = Math.max(0, (textHeight - estimatedTextHeight) / 2);

  // Function to measure text dimensions accurately
  const measureTextDimensions = (text: string): { width: number; height: number } => {
    if (!measureCanvasRef.current) {
      measureCanvasRef.current = document.createElement('canvas');
    }

    const canvas = measureCanvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return { width: 120, height: 40 };

    ctx.font = `${fontSize}px ${fontFamily}`;

    // Handle empty text - return minimal but usable size
    if (!text || text.trim() === '') {
      return {
        width: 100,
        height: Math.max(40, fontSize * lineHeight + 8)
      };
    }

    // Split text into lines and measure each
    const lines = text.split('\n');
    let maxWidth = 0;

    for (const line of lines) {
      if (line.trim() === '') {
        // Empty line - minimal width
        maxWidth = Math.max(maxWidth, 10);
      } else {
        const metrics = ctx.measureText(line);
        maxWidth = Math.max(maxWidth, metrics.width);
      }
    }

    // Calculate total height based on number of lines
    const totalHeight = lines.length * fontSize * lineHeight;

    // Fixed padding that matches the region's internal structure
    // The region has 4px offset (x=4, y=4) and reduces width/height by 8px
    const regionPadding = 8; // This accounts for the 4px on each side
    const textPadding = 8; // Additional padding for text comfort

    // Calculate final dimensions with consistent padding
    const finalWidth = maxWidth + regionPadding + textPadding;
    const finalHeight = totalHeight + regionPadding + textPadding;

    // Apply reasonable minimums
    const minWidth = 80;
    const minHeight = fontSize * lineHeight + regionPadding;

    return {
      width: Math.max(minWidth, finalWidth),
      height: Math.max(minHeight, finalHeight)
    };
  };

  const textareaStyles = {
    position: 'absolute' as const,
    top: `${regionY + textY}px`, // Add the 4px offset
    left: `${regionX + textX}px`, // Add the 4px offset
    width: `${textWidth}px`, // Use the reduced width
    height: `${textHeight}px`, // Use the reduced height
    fontSize: `${fontSize}px`,
    fontFamily: fontFamily,
    border: 'none',
    padding: `${verticalPadding}px 4px`, // Add some horizontal padding for better text positioning
    margin: '0px',
    overflow: 'hidden' as const,
    background: 'none',
    outline: 'none',
    resize: 'none' as const,
    lineHeight: lineHeight,
    textAlign: textAlign as 'left' | 'center' | 'right' | 'justify',
    color: region?.font_color || '#000000',
    minHeight: '1em',
    boxSizing: 'border-box' as const,
    // Remove any transforms for now to keep it simple
    transform: 'none',
    transformOrigin: 'left top' as const,
  };

  // Focus and position cursor - simplified approach
  useEffect(() => {
    if (!textareaRef.current) return;

    const textarea = textareaRef.current;

    // Use requestAnimationFrame to ensure the textarea is fully rendered before focusing
    const focusTextarea = () => {
      requestAnimationFrame(() => {
        if (textarea && document.contains(textarea)) {
          textarea.focus();

          // For now, just position cursor at the end of text
          // This provides a better UX than selecting all text
          const textLength = textarea.value.length;
          textarea.setSelectionRange(textLength, textLength);
        }
      });
    };

    // Try to focus immediately
    focusTextarea();

    // Also try after a short delay as fallback
    const timeoutId = setTimeout(focusTextarea, 10);

    return () => {
      clearTimeout(timeoutId);
    };
  }, []); // Only run on mount

  // Handle event listeners
  useEffect(() => {
    if (!textareaRef.current || !textNode) return;

    const textarea = textareaRef.current;

    // Track if we've added the outside click listener
    let outsideClickAdded = false;
    let canvasClickAdded = false;

    const handleOutsideClick = (e: MouseEvent) => {
      // Check if the click target is the textarea or its descendants
      const target = e.target as Element;
      if (target && textarea) {
        // Check if click is outside textarea
        const isOutsideTextarea = target !== textarea && !textarea.contains(target);

        // Check if click is on canvas, editor elements, or canvas container
        const isOnCanvas = target.tagName === 'CANVAS' || target.closest('canvas');
        const isOnEditor = target.closest('[data-editor]') || target.closest('.konvajs-content');
        const isOnCanvasContainer = target.closest('[data-canvas-container]');

        // Also check if click is on any part of the canvas workspace (including the gray background)
        const isOnCanvasWorkspace = target.closest('.bg-gray-100') && !target.closest('textarea');

        if (isOutsideTextarea && (isOnCanvas || isOnEditor || isOnCanvasContainer || isOnCanvasWorkspace)) {
          // Resize based on final content before closing
          if (onResize) {
            const newDimensions = measureTextDimensions(textarea.value);
            onResize(newDimensions);
          }
          onChange(textarea.value);
          onClose();
        }
      }
    };

    // Handle canvas click outside event (for Konva elements)
    const handleCanvasClickOutside = () => {
      // Resize based on final content before closing
      if (onResize) {
        const newDimensions = measureTextDimensions(textarea.value);
        onResize(newDimensions);
      }
      onChange(textarea.value);
      onClose();
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Enter' && e.shiftKey) {
        e.preventDefault();
        // Resize based on final content before closing
        if (onResize) {
          const newDimensions = measureTextDimensions(textarea.value);
          onResize(newDimensions);
        }
        onChange(textarea.value);
        onClose();
      }
      if (e.key === 'Escape') {
        e.preventDefault();
        // Resize based on final content before closing
        if (onResize) {
          const newDimensions = measureTextDimensions(textarea.value);
          onResize(newDimensions);
        }
        onChange(textarea.value);
        onClose();
      }
    };

    const handleInput = () => {
      // For now, let's disable auto-resize during typing to prevent the cumulative growth issue
      // We'll only resize when the user finishes editing
      return;
    };

    // Add event listeners immediately
    textarea.addEventListener('keydown', handleKeyDown);
    textarea.addEventListener('input', handleInput);

    // Add outside click listener immediately using bubble phase
    window.addEventListener('click', handleOutsideClick, false);
    outsideClickAdded = true;

    // Add canvas click outside listener for Konva events
    window.addEventListener('canvas-click-outside', handleCanvasClickOutside);
    canvasClickAdded = true;

    return () => {
      // Clear any pending resize timeout
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }

      // Remove event listeners
      textarea.removeEventListener('keydown', handleKeyDown);
      textarea.removeEventListener('input', handleInput);

      // Only remove outside click listener if it was added
      if (outsideClickAdded) {
        window.removeEventListener('click', handleOutsideClick, false);
      }

      // Remove canvas click listener if it was added
      if (canvasClickAdded) {
        window.removeEventListener('canvas-click-outside', handleCanvasClickOutside);
      }
    };
  }, [textNode, onChange, onClose, onResize]); // Include onResize to ensure event handlers are updated

  const textValue = initialText !== undefined ? initialText : (textNode?.text() || "");

  return (
    <Html>
      <textarea
        ref={textareaRef}
        style={textareaStyles}
        defaultValue={textValue}
        placeholder="Enter translation..."
        autoFocus
      />
    </Html>
  );
}
