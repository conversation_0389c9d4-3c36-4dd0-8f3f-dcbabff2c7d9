"""
Anthropic Claude LLM provider client.
"""
import base64
import json
from typing import List, Optional, Dict, Any

import httpx

from src.constants import LLMProvider
from src.llm_providers.base import (
    BaseLLMClient, LLMResponse, LLMMessage,
    LLMAPIError, LLMRateLimitError, LLMAuthenticationError, LLMValidationError
)


class ClaudeClient(BaseLLMClient):
    """Anthropic Claude API client."""
    
    BASE_URL = "https://api.anthropic.com/v1"
    DEFAULT_MODEL = "claude-3-5-sonnet-20241022"
    
    AVAILABLE_MODELS = [
        "claude-3-5-sonnet-20241022",
        "claude-3-5-haiku-20241022",
        "claude-3-opus-20240229",
        "claude-3-sonnet-20240229",
        "claude-3-haiku-20240307"
    ]
    
    def __init__(self, api_key: str, **kwargs):
        super().__init__(api_key, **kwargs)
        self.client = httpx.AsyncClient(
            base_url=self.BASE_URL,
            headers={
                "x-api-key": self.api_key,
                "anthropic-version": "2023-06-01",
                "content-type": "application/json"
            },
            timeout=60.0
        )
    
    async def generate_text(
        self,
        messages: List[LLMMessage],
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        **kwargs
    ) -> LLMResponse:
        """Generate text using Claude."""
        model = model or self.DEFAULT_MODEL
        max_tokens = max_tokens or 4096
        temperature = temperature or 0.7
        
        # Prepare messages for Claude format
        claude_messages = []
        system_message = None
        
        for msg in messages:
            if msg.role == "system":
                system_message = msg.content
            else:
                claude_messages.append({
                    "role": msg.role,
                    "content": msg.content
                })
        
        payload = {
            "model": model,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "messages": claude_messages,
            **kwargs
        }
        
        if system_message:
            payload["system"] = system_message
        
        try:
            response = await self.client.post("/messages", json=payload)
            response.raise_for_status()
            
            data = response.json()
            
            return LLMResponse(
                content=data["content"][0]["text"],
                provider=LLMProvider.CLAUDE,
                model=model,
                usage=data.get("usage"),
                metadata={
                    "stop_reason": data.get("stop_reason"),
                    "stop_sequence": data.get("stop_sequence")
                },
                raw_response=data
            )
            
        except httpx.HTTPStatusError as e:
            await self._handle_http_error(e)
        except Exception as e:
            raise LLMAPIError(f"Claude API error: {str(e)}", LLMProvider.CLAUDE)
    
    async def generate_text_with_image(
        self,
        messages: List[LLMMessage],
        image_data: bytes,
        image_format: str,
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        **kwargs
    ) -> LLMResponse:
        """Generate text with image input using Claude."""
        model = model or self.DEFAULT_MODEL
        max_tokens = max_tokens or 4096
        temperature = temperature or 0.7
        
        # Encode image to base64
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        
        # Prepare messages with image
        claude_messages = []
        system_message = None
        
        for msg in messages:
            if msg.role == "system":
                system_message = msg.content
            elif msg.role == "user":
                # Add image to the first user message
                claude_messages.append({
                    "role": "user",
                    "content": [
                        {
                            "type": "image",
                            "source": {
                                "type": "base64",
                                "media_type": f"image/{image_format}",
                                "data": image_base64
                            }
                        },
                        {
                            "type": "text",
                            "text": msg.content
                        }
                    ]
                })
            else:
                claude_messages.append({
                    "role": msg.role,
                    "content": msg.content
                })
        
        payload = {
            "model": model,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "messages": claude_messages,
            **kwargs
        }
        
        if system_message:
            payload["system"] = system_message
        
        try:
            response = await self.client.post("/messages", json=payload)
            response.raise_for_status()
            
            data = response.json()
            
            return LLMResponse(
                content=data["content"][0]["text"],
                provider=LLMProvider.CLAUDE,
                model=model,
                usage=data.get("usage"),
                metadata={
                    "stop_reason": data.get("stop_reason"),
                    "stop_sequence": data.get("stop_sequence"),
                    "image_format": image_format
                },
                raw_response=data
            )
            
        except httpx.HTTPStatusError as e:
            await self._handle_http_error(e)
        except Exception as e:
            raise LLMAPIError(f"Claude API error: {str(e)}", LLMProvider.CLAUDE)
    
    async def _handle_http_error(self, error: httpx.HTTPStatusError):
        """Handle HTTP errors from Claude API."""
        status_code = error.response.status_code
        
        try:
            error_data = error.response.json()
            error_message = error_data.get("error", {}).get("message", str(error))
            error_type = error_data.get("error", {}).get("type", "unknown")
        except:
            error_message = str(error)
            error_type = "unknown"
        
        if status_code == 401:
            raise LLMAuthenticationError(f"Claude authentication error: {error_message}", LLMProvider.CLAUDE)
        elif status_code == 429:
            raise LLMRateLimitError(f"Claude rate limit exceeded: {error_message}", LLMProvider.CLAUDE)
        elif status_code == 400:
            raise LLMValidationError(f"Claude validation error: {error_message}", LLMProvider.CLAUDE)
        else:
            raise LLMAPIError(f"Claude API error ({status_code}): {error_message}", LLMProvider.CLAUDE, error_type)
    
    def get_provider(self) -> LLMProvider:
        """Get the provider type."""
        return LLMProvider.CLAUDE
    
    def get_default_model(self) -> str:
        """Get the default model for Claude."""
        return self.DEFAULT_MODEL
    
    def get_available_models(self) -> List[str]:
        """Get list of available Claude models."""
        return self.AVAILABLE_MODELS.copy()
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
