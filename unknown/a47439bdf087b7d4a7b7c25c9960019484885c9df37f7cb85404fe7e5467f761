{"name": "ho-trans-backend", "version": "0.1.0", "description": "Backend API for ho-trans manga translation tool", "scripts": {"dev": "bash -c 'source .venv/bin/activate 2>/dev/null || true && uvicorn src.main:app --reload --host 0.0.0.0 --port 8000'", "start": "bash -c 'source .venv/bin/activate 2>/dev/null || true && uvicorn src.main:app --host 0.0.0.0 --port 8000'", "test": "bash -c 'source .venv/bin/activate && pytest tests/ -v --cov=src --cov-report=html'", "test:unit": "bash -c 'source .venv/bin/activate && pytest tests/ -v -m \"not integration\"'", "test:integration": "bash -c 'source .venv/bin/activate && pytest tests/ -v -m integration'", "lint": "bash -c 'source .venv/bin/activate && ruff check src/ && black --check src/ && isort --check-only src/'", "lint:fix": "bash -c 'source .venv/bin/activate && ruff check --fix src/ && black src/ && isort src/'", "typecheck": "bash -c 'source .venv/bin/activate && mypy src/'", "db:init": "bash -c 'source .venv/bin/activate && PYTHONPATH=. python src/database_init.py init'", "db:reset": "bash -c 'source .venv/bin/activate && PYTHONPATH=. python src/database_init.py reset'", "db:check": "bash -c 'source .venv/bin/activate && PYTHONPATH=. python src/database_init.py check'", "db:seed": "bash -c 'source .venv/bin/activate && PYTHONPATH=. python src/database_init.py seed'", "db:migrate": "bash -c 'source .venv/bin/activate && alembic upgrade head'", "setup": "cd ../.. && ./scripts/setup-backend.sh", "clean": "find . -name '__pycache__' -exec rm -rf {} + 2>/dev/null || true && find . -name '*.pyc' -exec rm -f {} + 2>/dev/null || true && rm -rf .coverage htmlcov/ .pytest_cache/ .mypy_cache/"}, "dependencies": {}, "devDependencies": {}, "engines": {"python": ">=3.11"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/ho-trans.git", "directory": "packages/backend"}, "keywords": ["manga", "translation", "ocr", "ai", "<PERSON><PERSON><PERSON>", "python"], "author": "ho-trans team", "license": "MIT"}