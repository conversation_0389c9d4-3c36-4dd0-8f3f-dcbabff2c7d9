$ next build
   ▲ Next.js 15.3.4

   Creating an optimized production build ...
 ✓ Compiled successfully in 0ms
   Linting and checking validity of types ...
   Collecting page data ...
   Generating static pages (0/6) ...
   Generating static pages (1/6) 
   Generating static pages (2/6) 
   Generating static pages (4/6) 
 ✓ Generating static pages (6/6)
   Finalizing page optimization ...
   Collecting build traces ...

Route (app)                                 Size  First Load JS
┌ ○ /                                    3.44 kB         105 kB
├ ○ /_not-found                            975 B         103 kB
└ ○ /editor                                18 kB         120 kB
+ First Load JS shared by all             102 kB
  ├ chunks/315-d7ec4046e6b4cebd.js       46.3 kB
  ├ chunks/87c73c54-19aac60e07ffce43.js  53.2 kB
  └ other shared chunks (total)           2.1 kB


○  (Static)  prerendered as static content

