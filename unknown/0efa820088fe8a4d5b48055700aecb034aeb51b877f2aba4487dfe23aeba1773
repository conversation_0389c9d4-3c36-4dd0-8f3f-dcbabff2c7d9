---
# Models System Definitions
# SQLAlchemy models and Pydantic schemas

models:
  description: "Data models for ho-trans manga translation application"
  intent: "Define data structures for persistence and API communication"
  
  base_models:
    description: "Base model classes providing common functionality"
    intent: "Establish consistent patterns for all domain models"
    implementation_status: "COMPLETE"
    implementation_gaps: []
    depends_on: []
    files:
      - "src/models.py"
    
    components:
      base_model:
        description: "SQLAlchemy base model with common fields"
        intent: "Provide UUID primary keys and automatic timestamps"
        features:
          - "UUID primary key generation"
          - "Automatic created_at timestamp"
          - "Automatic updated_at timestamp"
          - "Consistent table naming"
          - "Common query methods"
        fields:
          - name: "id"
            type: "String(36)"
            description: "UUID primary key"
          - name: "created_at"
            type: "DateTime"
            description: "Creation timestamp"
          - name: "updated_at"
            type: "DateTime"
            description: "Last update timestamp"

  domain_models:
    projects:
      description: "Project management domain models"
      intent: "Model manga translation projects, pages, and text regions"
      implementation_status: "COMPLETE"
      implementation_gaps: []
      depends_on:
        - "base_models"
      files:
        - "src/projects/models.py"
      
      models:
        project:
          description: "Main project entity"
          intent: "Represent a manga translation project"
          table_name: "project"
          inherits: "BaseModel"
          fields:
            - name: "name"
              type: "String(255)"
              constraints: ["NOT NULL"]
              description: "Project name"
            - name: "description"
              type: "Text"
              description: "Project description"
            - name: "status"
              type: "String(50)"
              constraints: ["NOT NULL"]
              description: "Project status enum"
            - name: "source_language"
              type: "String(50)"
              constraints: ["NOT NULL"]
              description: "Source language code"
            - name: "target_language"
              type: "String(50)"
              constraints: ["NOT NULL"]
              description: "Target language code"
          relationships:
            - name: "pages"
              target: "ProjectPage"
              type: "one_to_many"
              back_populates: "project"
              cascade: "all, delete-orphan"

        project_page:
          description: "Individual manga page within a project"
          intent: "Store page metadata and file information"
          table_name: "project_page"
          inherits: "BaseModel"
          fields:
            - name: "project_id"
              type: "String(36)"
              constraints: ["NOT NULL", "FOREIGN KEY"]
              description: "Reference to parent project"
            - name: "page_number"
              type: "Integer"
              constraints: ["NOT NULL"]
              description: "Page number within project"
            - name: "original_filename"
              type: "String(255)"
              constraints: ["NOT NULL"]
              description: "Original uploaded filename"
            - name: "file_path"
              type: "String(500)"
              constraints: ["NOT NULL"]
              description: "File storage path"
            - name: "file_size"
              type: "Integer"
              constraints: ["NOT NULL"]
              description: "File size in bytes"
            - name: "image_width"
              type: "Integer"
              description: "Image width in pixels"
            - name: "image_height"
              type: "Integer"
              description: "Image height in pixels"
            - name: "ocr_status"
              type: "String(50)"
              constraints: ["NOT NULL"]
              description: "OCR processing status"
          relationships:
            - name: "project"
              target: "Project"
              type: "many_to_one"
              back_populates: "pages"
            - name: "text_regions"
              target: "TextRegion"
              type: "one_to_many"
              back_populates: "page"
              cascade: "all, delete-orphan"
            - name: "ocr_jobs"
              target: "OCRJob"
              type: "one_to_many"
              back_populates: "page"

        text_region:
          description: "Text region within a manga page"
          intent: "Store text region coordinates, content, and styling"
          table_name: "text_region"
          inherits: "BaseModel"
          fields:
            - name: "page_id"
              type: "String(36)"
              constraints: ["NOT NULL", "FOREIGN KEY"]
              description: "Reference to parent page"
            - name: "region_type"
              type: "String(50)"
              constraints: ["NOT NULL"]
              description: "Type of text region"
            - name: "x"
              type: "Float"
              constraints: ["NOT NULL"]
              description: "X coordinate (normalized)"
            - name: "y"
              type: "Float"
              constraints: ["NOT NULL"]
              description: "Y coordinate (normalized)"
            - name: "width"
              type: "Float"
              constraints: ["NOT NULL"]
              description: "Width (normalized)"
            - name: "height"
              type: "Float"
              constraints: ["NOT NULL"]
              description: "Height (normalized)"
            - name: "original_text"
              type: "Text"
              description: "Original detected text"
            - name: "confidence_score"
              type: "Float"
              description: "OCR confidence score"
            - name: "translated_text"
              type: "Text"
              description: "Translated text"
            - name: "translation_status"
              type: "String(50)"
              constraints: ["NOT NULL"]
              description: "Translation status"
            - name: "font_family"
              type: "String(100)"
              description: "Font family for rendering"
            - name: "font_size"
              type: "Integer"
              description: "Font size for rendering"
            - name: "font_color"
              type: "String(7)"
              description: "Font color (hex)"
            - name: "background_color"
              type: "String(7)"
              description: "Background color (hex)"
          relationships:
            - name: "page"
              target: "ProjectPage"
              type: "many_to_one"
              back_populates: "text_regions"
            - name: "translation_jobs"
              target: "TranslationJob"
              type: "one_to_many"
              back_populates: "text_region"

    ocr:
      description: "OCR processing domain models"
      intent: "Model OCR jobs and results"
      implementation_status: "COMPLETE"
      implementation_gaps: []
      depends_on:
        - "base_models"
        - "projects"
      files:
        - "src/ocr/models.py"
      
      models:
        ocr_job:
          description: "OCR processing job"
          intent: "Track OCR processing requests and status"
          table_name: "ocr_job"
          inherits: "BaseModel"
          fields:
            - name: "page_id"
              type: "String(36)"
              constraints: ["NOT NULL", "FOREIGN KEY"]
              description: "Reference to project page"
            - name: "status"
              type: "String(50)"
              constraints: ["NOT NULL"]
              description: "Job processing status"
            - name: "provider"
              type: "String(50)"
              constraints: ["NOT NULL"]
              description: "LLM provider used"
            - name: "prompt_used"
              type: "Text"
              description: "OCR prompt used"
            - name: "raw_response"
              type: "Text"
              description: "Raw LLM response"
            - name: "processing_time_seconds"
              type: "Float"
              description: "Processing time"
            - name: "error_message"
              type: "Text"
              description: "Error message if failed"
            - name: "retry_count"
              type: "Integer"
              constraints: ["NOT NULL", "DEFAULT 0"]
              description: "Retry attempts"
            - name: "total_regions_detected"
              type: "Integer"
              constraints: ["NOT NULL", "DEFAULT 0"]
              description: "Number of regions detected"
            - name: "average_confidence"
              type: "Float"
              description: "Average confidence score"
          relationships:
            - name: "page"
              target: "ProjectPage"
              type: "many_to_one"
              back_populates: "ocr_jobs"
            - name: "results"
              target: "OCRResult"
              type: "one_to_many"
              back_populates: "job"
              cascade: "all, delete-orphan"

        ocr_result:
          description: "Individual OCR detection result"
          intent: "Store detected text with coordinates and confidence"
          table_name: "ocr_result"
          inherits: "BaseModel"
          fields:
            - name: "job_id"
              type: "String(36)"
              constraints: ["NOT NULL", "FOREIGN KEY"]
              description: "Reference to OCR job"
            - name: "detected_text"
              type: "Text"
              constraints: ["NOT NULL"]
              description: "Detected text content"
            - name: "confidence_score"
              type: "Float"
              description: "Detection confidence"
            - name: "region_type"
              type: "String(50)"
              description: "Type of text region"
            - name: "x"
              type: "Float"
              description: "X coordinate"
            - name: "y"
              type: "Float"
              description: "Y coordinate"
            - name: "width"
              type: "Float"
              description: "Region width"
            - name: "height"
              type: "Float"
              description: "Region height"
            - name: "metadata"
              type: "JSON"
              description: "Additional metadata"
          relationships:
            - name: "job"
              target: "OCRJob"
              type: "many_to_one"
              back_populates: "results"

    translation:
      description: "Translation processing domain models"
      intent: "Model translation jobs, alternatives, and templates"
      implementation_status: "COMPLETE"
      implementation_gaps: []
      depends_on:
        - "base_models"
        - "projects"
      files:
        - "src/translation/models.py"
      
      models:
        translation_job:
          description: "Translation processing job"
          intent: "Track translation requests and results"
          table_name: "translation_job"
          inherits: "BaseModel"
          fields:
            - name: "text_region_id"
              type: "String(36)"
              constraints: ["NOT NULL", "FOREIGN KEY"]
              description: "Reference to text region"
            - name: "status"
              type: "String(50)"
              constraints: ["NOT NULL"]
              description: "Job processing status"
            - name: "provider"
              type: "String(50)"
              constraints: ["NOT NULL"]
              description: "LLM provider used"
            - name: "source_language"
              type: "String(50)"
              constraints: ["NOT NULL"]
              description: "Source language"
            - name: "target_language"
              type: "String(50)"
              constraints: ["NOT NULL"]
              description: "Target language"
            - name: "original_text"
              type: "Text"
              constraints: ["NOT NULL"]
              description: "Original text to translate"
            - name: "translated_text"
              type: "Text"
              description: "Translated text result"
            - name: "prompt_used"
              type: "Text"
              description: "Translation prompt used"
            - name: "raw_response"
              type: "Text"
              description: "Raw LLM response"
            - name: "processing_time_seconds"
              type: "Float"
              description: "Processing time"
            - name: "confidence_score"
              type: "Float"
              description: "Translation confidence"
            - name: "quality_score"
              type: "Float"
              description: "Translation quality score"
            - name: "error_message"
              type: "Text"
              description: "Error message if failed"
            - name: "retry_count"
              type: "Integer"
              constraints: ["NOT NULL", "DEFAULT 0"]
              description: "Retry attempts"
            - name: "metadata"
              type: "JSON"
              description: "Additional metadata"
          relationships:
            - name: "text_region"
              target: "TextRegion"
              type: "many_to_one"
              back_populates: "translation_jobs"
            - name: "alternatives"
              target: "TranslationAlternative"
              type: "one_to_many"
              back_populates: "job"
              cascade: "all, delete-orphan"

        translation_alternative:
          description: "Alternative translation option"
          intent: "Store multiple translation options for selection"
          table_name: "translation_alternative"
          inherits: "BaseModel"
          fields:
            - name: "job_id"
              type: "String(36)"
              constraints: ["NOT NULL", "FOREIGN KEY"]
              description: "Reference to translation job"
            - name: "translated_text"
              type: "Text"
              constraints: ["NOT NULL"]
              description: "Alternative translation"
            - name: "confidence_score"
              type: "Float"
              description: "Confidence score"
            - name: "quality_score"
              type: "Float"
              description: "Quality score"
            - name: "rank"
              type: "Integer"
              constraints: ["NOT NULL"]
              description: "Ranking order"
            - name: "is_selected"
              type: "String(5)"
              constraints: ["NOT NULL", "DEFAULT 'false'"]
              description: "Whether selected (SQLite boolean)"
            - name: "metadata"
              type: "JSON"
              description: "Additional metadata"
          relationships:
            - name: "job"
              target: "TranslationJob"
              type: "many_to_one"
              back_populates: "alternatives"

        translation_template:
          description: "Reusable translation template"
          intent: "Store translation patterns for common phrases"
          table_name: "translation_template"
          inherits: "BaseModel"
          fields:
            - name: "name"
              type: "String(255)"
              constraints: ["NOT NULL"]
              description: "Template name"
            - name: "description"
              type: "Text"
              description: "Template description"
            - name: "source_language"
              type: "String(50)"
              constraints: ["NOT NULL"]
              description: "Source language"
            - name: "target_language"
              type: "String(50)"
              constraints: ["NOT NULL"]
              description: "Target language"
            - name: "source_pattern"
              type: "Text"
              constraints: ["NOT NULL"]
              description: "Source text pattern"
            - name: "target_pattern"
              type: "Text"
              constraints: ["NOT NULL"]
              description: "Target text pattern"
            - name: "usage_count"
              type: "Integer"
              constraints: ["NOT NULL", "DEFAULT 0"]
              description: "Usage count"
            - name: "category"
              type: "String(100)"
              description: "Template category"
            - name: "tags"
              type: "JSON"
              description: "Template tags"
            - name: "average_quality_score"
              type: "Float"
              description: "Average quality score"

  validation:
    description: "Model validation and constraints"
    intent: "Ensure data integrity and business rule enforcement"
    implementation_status: "COMPLETE"
    implementation_gaps: []
    depends_on:
      - "domain_models"
    features:
      - "SQLAlchemy column constraints"
      - "Foreign key relationships"
      - "Enum value validation"
      - "JSON field validation"
      - "Custom validation methods"

  serialization:
    description: "Model serialization and deserialization"
    intent: "Convert between database models and API representations"
    implementation_status: "COMPLETE"
    implementation_gaps: []
    depends_on:
      - "domain_models"
      - "schemas"
    features:
      - "Automatic model to dict conversion"
      - "Relationship loading strategies"
      - "Lazy loading configuration"
      - "Custom serialization methods"
      - "Performance optimization"

schemas:
  description: "Pydantic schemas for API request/response validation"
  intent: "Define data structures for API communication with validation"

  base_schemas:
    description: "Base schema classes and common patterns"
    intent: "Provide consistent schema patterns across all modules"
    implementation_status: "COMPLETE"
    implementation_gaps: []
    depends_on: []
    files:
      - "src/schemas.py"

    components:
      custom_model:
        description: "Base Pydantic model with custom configuration"
        intent: "Provide consistent field naming and validation"
        features:
          - "Automatic alias generation (camelCase)"
          - "Custom validation rules"
          - "Consistent error messages"
          - "Performance optimization"

      base_schema:
        description: "Base schema with common fields"
        intent: "Provide standard fields for all entities"
        fields:
          - name: "id"
            type: "str"
            description: "Entity UUID"
          - name: "created_at"
            type: "datetime"
            description: "Creation timestamp"
          - name: "updated_at"
            type: "datetime"
            description: "Last update timestamp"

      response_schemas:
        description: "Standard response schemas"
        intent: "Provide consistent API response formats"
        schemas:
          - name: "SuccessResponse"
            description: "Standard success response"
            fields:
              - name: "message"
                type: "str"
                description: "Success message"
          - name: "ErrorResponse"
            description: "Standard error response"
            fields:
              - name: "detail"
                type: "str"
                description: "Error details"
              - name: "error_code"
                type: "str"
                description: "Error code"

  domain_schemas:
    projects:
      description: "Project management schemas"
      intent: "Define request/response schemas for project operations"
      implementation_status: "COMPLETE"
      implementation_gaps: []
      depends_on:
        - "base_schemas"
      files:
        - "src/projects/schemas.py"

      schemas:
        project_create:
          description: "Schema for creating new projects"
          intent: "Validate project creation requests"
          fields:
            - name: "name"
              type: "str"
              constraints: ["required", "min_length=1", "max_length=255"]
            - name: "description"
              type: "Optional[str]"
              constraints: ["max_length=1000"]
            - name: "source_language"
              type: "str"
              constraints: ["required"]
            - name: "target_language"
              type: "str"
              constraints: ["required"]

        project_update:
          description: "Schema for updating projects"
          intent: "Validate project update requests"
          fields:
            - name: "name"
              type: "Optional[str]"
              constraints: ["min_length=1", "max_length=255"]
            - name: "description"
              type: "Optional[str]"
              constraints: ["max_length=1000"]
            - name: "status"
              type: "Optional[ProjectStatus]"
              constraints: ["enum_validation"]

        project_response:
          description: "Schema for project API responses"
          intent: "Structure project data for API responses"
          inherits: "BaseSchema"
          fields:
            - name: "name"
              type: "str"
            - name: "description"
              type: "Optional[str]"
            - name: "status"
              type: "ProjectStatus"
            - name: "source_language"
              type: "str"
            - name: "target_language"
              type: "str"
            - name: "page_count"
              type: "int"

        project_detail_response:
          description: "Schema for detailed project responses"
          intent: "Include project with related pages"
          inherits: "ProjectResponse"
          fields:
            - name: "pages"
              type: "List[ProjectPageResponse]"

    ocr:
      description: "OCR processing schemas"
      intent: "Define schemas for OCR operations"
      implementation_status: "COMPLETE"
      implementation_gaps: []
      depends_on:
        - "base_schemas"
      files:
        - "src/ocr/schemas.py"

      schemas:
        ocr_job_create:
          description: "Schema for creating OCR jobs"
          fields:
            - name: "page_id"
              type: "str"
              constraints: ["required", "uuid_format"]
            - name: "provider"
              type: "LLMProvider"
              constraints: ["required", "enum_validation"]
            - name: "custom_prompt"
              type: "Optional[str]"
              constraints: ["max_length=2000"]

        ocr_job_response:
          description: "Schema for OCR job responses"
          inherits: "BaseSchema"
          fields:
            - name: "page_id"
              type: "str"
            - name: "status"
              type: "OCRStatus"
            - name: "provider"
              type: "LLMProvider"
            - name: "processing_time_seconds"
              type: "Optional[float]"
            - name: "total_regions_detected"
              type: "int"
            - name: "average_confidence"
              type: "Optional[float]"

    translation:
      description: "Translation processing schemas"
      intent: "Define schemas for translation operations"
      implementation_status: "COMPLETE"
      implementation_gaps: []
      depends_on:
        - "base_schemas"
      files:
        - "src/translation/schemas.py"

      schemas:
        translation_job_create:
          description: "Schema for creating translation jobs"
          fields:
            - name: "text_region_id"
              type: "str"
              constraints: ["required", "uuid_format"]
            - name: "provider"
              type: "LLMProvider"
              constraints: ["required", "enum_validation"]
            - name: "source_language"
              type: "str"
              constraints: ["required"]
            - name: "target_language"
              type: "str"
              constraints: ["required"]
            - name: "original_text"
              type: "str"
              constraints: ["required", "min_length=1"]

        translation_job_response:
          description: "Schema for translation job responses"
          inherits: "BaseSchema"
          fields:
            - name: "text_region_id"
              type: "str"
            - name: "status"
              type: "TranslationStatus"
            - name: "provider"
              type: "LLMProvider"
            - name: "original_text"
              type: "str"
            - name: "translated_text"
              type: "Optional[str]"
            - name: "confidence_score"
              type: "Optional[float]"
            - name: "quality_score"
              type: "Optional[float]"

    llm_providers:
      description: "LLM provider schemas"
      intent: "Define schemas for LLM operations"
      implementation_status: "COMPLETE"
      implementation_gaps: []
      depends_on:
        - "base_schemas"
      files:
        - "src/llm_providers/schemas.py"

      schemas:
        llm_text_generation_request:
          description: "Schema for text generation requests"
          fields:
            - name: "prompt"
              type: "str"
              constraints: ["required", "min_length=1"]
            - name: "provider"
              type: "LLMProvider"
              constraints: ["required"]
            - name: "model"
              type: "Optional[str]"
            - name: "temperature"
              type: "Optional[float]"
              constraints: ["ge=0", "le=2"]
            - name: "max_tokens"
              type: "Optional[int]"
              constraints: ["gt=0", "le=4000"]

        llm_generation_response:
          description: "Schema for LLM generation responses"
          fields:
            - name: "content"
              type: "str"
            - name: "provider"
              type: "str"
            - name: "model"
              type: "str"
            - name: "usage"
              type: "Dict[str, int]"
            - name: "metadata"
              type: "Dict[str, Any]"
            - name: "processing_time_ms"
              type: "float"

  validation:
    description: "Schema validation rules and custom validators"
    intent: "Ensure data integrity and provide clear error messages"
    implementation_status: "COMPLETE"
    implementation_gaps: []
    depends_on:
      - "domain_schemas"
    features:
      - "Custom field validators"
      - "Cross-field validation"
      - "Enum validation"
      - "UUID format validation"
      - "Language code validation"
      - "File type validation"
      - "Coordinate range validation"
