#!/bin/bash

# Setup script for ho-trans backend
set -e

echo "🐍 Setting up ho-trans backend..."

# Check if we're in the right directory
if [ ! -f "packages/backend/pyproject.toml" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

cd packages/backend

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Error: Python 3 is required but not installed"
    exit 1
fi

# Check Python version
python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
required_version="3.11"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Error: Python $required_version or higher is required (found $python_version)"
    exit 1
fi

echo "✅ Python $python_version found"

# Check if virtual environment exists, create if not
if [ ! -d ".venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv .venv
fi

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source .venv/bin/activate

# Install dependencies
echo "📥 Installing Python dependencies..."
pip install --upgrade pip
pip install -r requirements/dev.txt

# Initialize database
echo "🗄️ Setting up database..."
if [ ! -f "ho_trans.db" ]; then
    echo "📊 Initializing database..."
    PYTHONPATH=. python src/database_init.py init
    
    echo "🌱 Seeding database with sample data..."
    PYTHONPATH=. python src/database_init.py seed
else
    echo "✅ Database already exists"
    echo "🔄 Running migrations..."
    alembic upgrade head
fi

echo "✅ Backend setup complete!"
echo ""
echo "To start the backend manually:"
echo "  cd packages/backend"
echo "  source .venv/bin/activate"
echo "  uvicorn src.main:app --reload --host 0.0.0.0 --port 8000"
echo ""
echo "Or use the monorepo command:"
echo "  bun run dev"
