# Ho-Trans 🎌📖

A comprehensive manga translation tool powered by AI, featuring OCR text detection and intelligent translation capabilities.

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18+ or **Bun** (recommended)
- **Python** 3.11+
- **Git**

### One-Command Setup

```bash
# Clone the repository
git clone <your-repo-url>
cd ho-trans

# Install dependencies and setup everything
bun install
bun run setup

# Start both frontend and backend
bun run dev
```

This will start:
- **Frontend**: http://localhost:3000 (Next.js)
- **Backend**: http://localhost:8000 (FastAPI)

## 📁 Project Structure

```
ho-trans/
├── packages/
│   ├── frontend/          # Next.js frontend application
│   │   ├── app/          # Next.js app directory
│   │   ├── components/   # React components
│   │   ├── lib/          # API client and utilities
│   │   └── package.json
│   └── backend/          # FastAPI backend application
│       ├── src/          # Python source code
│       ├── tests/        # Test suite
│       ├── requirements/ # Python dependencies
│       └── package.json
├── scripts/              # Setup and utility scripts
├── turbo.json           # Turborepo configuration
└── package.json         # Root package configuration
```

## 🛠 Available Commands

### Development

```bash
# Start both frontend and backend in development mode
bun run dev

# Start individual services
cd packages/frontend && bun run dev  # Frontend only
cd packages/backend && bun run dev   # Backend only
```

### Building

```bash
# Build all packages
bun run build

# Build individual packages
bun run build --filter=frontend
bun run build --filter=ho-trans-backend
```

### Testing

```bash
# Run all tests
bun run test

# Run frontend tests
bun run test --filter=frontend

# Run backend tests
bun run test --filter=ho-trans-backend
```

### Code Quality

```bash
# Lint all packages
bun run lint

# Type checking
bun run typecheck

# Clean build artifacts
bun run clean
```

## 🎯 Features

### Frontend (Next.js + React)
- **Canvas Editor**: Photo editor-style interface for manga editing
- **Project Management**: Create and organize translation projects
- **Text Region Tools**: Draw and manage text areas on manga pages
- **Font Styling**: Comprehensive typography controls
- **Real-time Preview**: See translations as you work

### Backend (FastAPI + Python)
- **OCR Processing**: AI-powered text detection using multiple LLM providers
- **Translation Engine**: Multi-provider translation with alternatives
- **Project API**: RESTful API for project and page management
- **Database**: SQLite with Alembic migrations
- **Async Architecture**: High-performance async/await patterns

### AI Integration
- **Multiple LLM Providers**: Claude, OpenAI, Gemini, DeepSeek
- **OCR Capabilities**: Automatic text detection and region creation
- **Translation Alternatives**: Multiple translation options per text
- **Custom Prompts**: Fine-tune AI behavior for specific needs

## 🔧 Configuration

### Environment Variables

**Frontend** (`.env.local`):
```bash
NEXT_PUBLIC_API_URL=http://localhost:8000
```

**Backend** (`.env` - optional):
```bash
DATABASE_URL=sqlite:///./ho_trans.db
CLAUDE_API_KEY=your_claude_key
OPENAI_API_KEY=your_openai_key
GEMINI_API_KEY=your_gemini_key
```

## 📚 Documentation

- [Frontend Documentation](packages/frontend/README.md)
- [Backend Documentation](packages/backend/README.md)
- [API Documentation](http://localhost:8000/docs) (when backend is running)

## 🧪 Development Workflow

1. **Setup**: Run `bun run setup` to initialize everything
2. **Development**: Use `bun run dev` for hot-reload development
3. **Testing**: Run `bun run test` before committing
4. **Linting**: Use `bun run lint` to check code quality
5. **Building**: Run `bun run build` to create production builds

## 🐛 Troubleshooting

### Backend Issues

```bash
# Reset Python environment
cd packages/backend
rm -rf venv
bun run setup

# Reset database
bun run db:reset
bun run db:seed
```

### Frontend Issues

```bash
# Clear Next.js cache
cd packages/frontend
rm -rf .next node_modules/.cache
bun install
```

### Turborepo Issues

```bash
# Clear Turbo cache
bunx turbo clean
bun run clean
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes
4. Run tests: `bun run test`
5. Commit changes: `git commit -m 'Add amazing feature'`
6. Push to branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with [Turborepo](https://turbo.build/) for monorepo management
- Frontend powered by [Next.js](https://nextjs.org/) and [Radix UI](https://www.radix-ui.com/)
- Backend built with [FastAPI](https://fastapi.tiangolo.com/)
- AI integration with multiple LLM providers
