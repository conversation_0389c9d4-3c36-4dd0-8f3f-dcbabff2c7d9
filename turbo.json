{"$schema": "https://turbo.build/schema.json", "tasks": {"dev": {"cache": false, "persistent": true, "dependsOn": ["^setup"]}, "build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**", "htmlcov/**"]}, "lint": {"outputs": []}, "typecheck": {"outputs": []}, "setup": {"cache": false, "outputs": []}, "clean": {"cache": false, "outputs": []}}}